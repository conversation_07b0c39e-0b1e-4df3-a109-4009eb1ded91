<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	
	const dispatch = createEventDispatcher();
	
	let messageText = '';
	let isTyping = false;
	let typingTimeout: number | null = null; // Browser setTimeout returns number
	
	function handleSend() {
		if (messageText.trim()) {
			dispatch('send', {
				content: messageText.trim(),
				type: 'TEXT'
			});
			messageText = '';
			stopTyping();
		}
	}
	
	function handleKeyPress(event: KeyboardEvent) {
		if (event.key === 'Enter' && !event.shiftKey) {
			event.preventDefault();
			handleSend();
		}
	}
	
	function handleInput() {
		// Emit typing indicator
		if (!isTyping) {
			isTyping = true;
			dispatch('typing', { isTyping: true });
		}
		
		// Clear existing timeout
		if (typingTimeout) {
			window.clearTimeout(typingTimeout);
		}
		
		// Set new timeout to stop typing indicator
		typingTimeout = window.setTimeout(() => {
			stopTyping();
		}, 1000);
	}
	
	function stopTyping() {
		if (isTyping) {
			isTyping = false;
			dispatch('typing', { isTyping: false });
		}
		if (typingTimeout) {
			window.clearTimeout(typingTimeout);
			typingTimeout = null;
		}
	}
	
	function handleFileClick() {
		// TODO: Implement file upload
		console.log('File upload clicked');
	}
</script>

<div class="border-t border-gray-200 px-4 py-3 bg-white">
	<div class="flex items-end space-x-2">
		<!-- File Upload Button -->
		<button
			on:click={handleFileClick}
			class="p-2 hover:bg-gray-100 rounded-lg transition-colors"
			title="Attach file"
		>
			<svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
			</svg>
		</button>
		
		<!-- Message Input -->
		<div class="flex-1">
			<textarea
				bind:value={messageText}
				on:keypress={handleKeyPress}
				on:input={handleInput}
				placeholder="Type a message..."
				rows="1"
				class="w-full resize-none rounded-lg border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
				style="min-height: 40px; max-height: 120px;"
			/>
		</div>
		
		<!-- Send Button -->
		<button
			on:click={handleSend}
			disabled={!messageText.trim()}
			class="p-2 rounded-lg transition-colors {messageText.trim() 
				? 'bg-blue-500 hover:bg-blue-600 text-white' 
				: 'bg-gray-100 text-gray-400 cursor-not-allowed'}"
			title="Send message"
		>
			<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
			</svg>
		</button>
	</div>
</div>

<style>
	textarea {
		overflow-y: auto;
	}
	
	textarea:focus {
		resize: none;
	}
</style>