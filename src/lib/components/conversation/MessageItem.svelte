<script lang="ts">
	import type { Message } from '$lib/types/customer';
	import { formatMessageTime } from '$lib/utils/messageFormatter';
	import { getInitials } from '$lib/utils/avatarGenerator';
	
	export let message: Message;
	export let showAvatar: boolean = true;
	
	function getAvatarColor(name: string) {
		// Generate consistent color based on name
		const colors = [
			'bg-blue-500',
			'bg-green-500',
			'bg-yellow-500',
			'bg-red-500',
			'bg-purple-500',
			'bg-pink-500',
			'bg-indigo-500',
		];
		
		let hash = 0;
		for (let i = 0; i < name.length; i++) {
			hash = name.charCodeAt(i) + ((hash << 5) - hash);
		}
		
		return colors[Math.abs(hash) % colors.length];
	}
	
	function renderMessageContent() {
		switch (message.message_type) {
			case 'IMAGE':
				return `<div class="text-blue-600">📷 Image message</div>`;
			case 'FILE':
				return `<div class="text-blue-600">📎 File attachment</div>`;
			default:
				// Convert newlines to <br> tags
				return message.message.replace(/\n/g, '<br>');
		}
	}
</script>

<div class="mb-4 flex {message.is_self ? 'justify-end' : 'justify-start'}">
	<div class="flex {message.is_self ? 'flex-row-reverse' : 'flex-row'} items-end max-w-xs lg:max-w-md">
		<!-- Avatar -->
		{#if !message.is_self && showAvatar}
			<div class="flex-shrink-0 mx-2">
				<div class="{getAvatarColor(message.user_name)} w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-medium">
					{getInitials(message.user_name)}
				</div>
			</div>
		{:else if !message.is_self}
			<div class="w-8 mx-2"></div>
		{/if}
		
		<!-- Message bubble -->
		<div class="flex flex-col {message.is_self ? 'items-end' : 'items-start'}">
			{#if showAvatar && !message.is_self}
				<span class="text-xs text-gray-500 mb-1 px-1">{message.user_name}</span>
			{/if}
			
			<div class="{message.is_self ? 'bg-blue-500 text-white' : 'bg-white border border-gray-200'} rounded-lg px-4 py-2 shadow-sm">
				<div class="text-sm break-words" style="word-break: break-word;">
					{@html renderMessageContent()}
				</div>
			</div>
			
			<div class="flex items-center mt-1 px-1 space-x-2">
				<span class="text-xs text-gray-400">
					{formatMessageTime(message.created_on)}
				</span>
				
				{#if message.is_self}
					<!-- Message status indicators -->
					{#if message.status === 'SENT'}
						<svg class="w-3 h-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
						</svg>
					{:else if message.status === 'DELIVERED'}
						<svg class="w-3 h-3 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
							<path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z" />
						</svg>
					{:else if message.status === 'READ'}
						<svg class="w-3 h-3 text-blue-500" fill="currentColor" viewBox="0 0 24 24">
							<path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z" />
						</svg>
					{/if}
				{/if}
			</div>
		</div>
	</div>
</div>