<script lang="ts">
    import { t } from '$lib/stores/i18n';
    import { enhance } from '$app/forms';
    import {
        Button,
        Modal,
        Label,
        Input,
        Checkbox,
        Alert,
    } from 'flowbite-svelte';
    import { handleEnhance } from '$lib/components/Enhance/SubmissionHandleEnhance.svelte';
    import { toastStore } from '$lib/stores/toastStore';

    export let user: any;

    import { page } from '$app/stores';

    $: role = $page.data.role;

    let editForm: HTMLFormElement;
    let editModalOpen = false;
    let selectInstances: any = null;
    
    // State variables for handling messages
    let isSubmitting = false;
    let isPasswordSubmitting = false;

    // Password modal state
    let passwordModalOpen = false;
    let passwordForm: HTMLFormElement;
    let passwordFormData = {
        new_password: '',
        confirm_password: ''
    };
    let passwordFieldsEverTyped = false;

    $: passwordsMatch = passwordFormData.new_password === passwordFormData.confirm_password 
        && passwordFormData.new_password.length > 0;

    // Handle form submission results
    const handleSubmissionResult = (result: any, isPassword: boolean = false) => {
        if (result.type === 'failure') {
            toastStore.add(result.data?.error || 'Operation failed', 'error');
        } else if (result.type === 'success') {
            toastStore.add(result.data?.res_msg || (isPassword ? 'Password changed successfully' : 'User updated successfully'), 'success');
            if (isPassword) {
                passwordModalOpen = false;
            } else {
                editModalOpen = false;
            }
        }
    };

    // Enhance handlers
    const handleEditEnhance = () => {
        return async ({ result, update }) => {
            isSubmitting = true;
            handleSubmissionResult(result);
            await update();
            isSubmitting = false;
        };
    };

    const handlePasswordEnhance = () => {
        return async ({ result, update }) => {
            isPasswordSubmitting = true;
            handleSubmissionResult(result, true);
            await update();
            isPasswordSubmitting = false;
        };
    };

    function openEditModal(user: any) {
        selectInstances = { ...user };
        editModalOpen = true;
    }

    // Watch modal state to clean up everything
    $: if (passwordModalOpen === false) {
        passwordFormData = {
            new_password: '',
            confirm_password: ''
        };
        passwordFieldsEverTyped = false;
    }

    // Form data with initial values from user
    let formData = {
        name: user.name,
        email: user.email,
        employee_id: user.employee_id,
        first_name: user.first_name,
        last_name: user.last_name,
        department: user.department || '',
        role: user.role,
        is_active: user.is_active
    };

    // onst roles = ['Admin', 'Supervisor', 'Agent']; // Update based on your available roles

</script>


<Button 
    color="none"
    class="w-full justify-start text-left hover:bg-gray-100 p-0"
    on:click={() => openEditModal(user)}>
    Edit User
</Button>
<Modal 
    bind:open={editModalOpen} 
    size="md" 
    outsideclose 
    class="{passwordModalOpen ? 'brightness-50 dark:brightness-80' : ''}">
    <div class="text-2xl font-semibold mb-4 text-black">Edit User</div>
    <div class="p-2">
        <form
            bind:this={editForm}
            action="?/update_user"
            method="POST"
            use:enhance={handleEditEnhance}
            class="space-y-3"
        >
            <input type="hidden" name="id" value={user.id}>
            <input type="hidden" name="username" value={user.username}>

            <div class="grid gap-2">
                <Label for="name" class="space-y-2 text-left">Name</Label>
                <Input
                    id="name"
                    name="name"
                    type="text"
                    placeholder="Enter name"
                    bind:value={formData.name}
                    required
                />
            </div>

            <div class="grid grid-cols-2 gap-4">
                <div class="grid gap-2">
                    <Label for="first_name" class="space-y-2 text-left">First name</Label>
                    <Input
                        id="first_name"
                        name="first_name"
                        type="text"
                        placeholder="Enter first name"
                        bind:value={formData.first_name}
                        required
                    />
                </div>
            
                <div class="grid gap-2">
                    <Label for="last_name" class="space-y-2 text-left">Last name</Label>
                    <Input
                        id="last_name"
                        name="last_name"
                        type="text"
                        placeholder="Enter last name"
                        bind:value={formData.last_name}
                        required
                    />
                </div>
            </div>

            <div class="grid grid-cols-2 gap-4">
                <div class="grid gap-2">
                    <Label for="email" class="space-y-2 text-left">Email</Label>
                    <Input
                        id="email"
                        name="email"
                        type="email"
                        placeholder="Enter email"
                        bind:value={formData.email}
                        required
                    />
                </div>
                {#if role === "Admin"}
                    <div class="grid text-left gap-2-left">
                        <Label for="password" class="space-y-2 text-left">{t('password')}</Label>
                        <div class="mt-2">
                            <Button
                                type="button"
                                class="w-auto bg-white border border-gray-300 hover:bg-gray-500 hover:text-white text-black font-medium rounded-lg transition-colors whitespace-nowrap"
                                on:click={(e) => {
                                    e.stopPropagation();
                                    passwordModalOpen = true;
                                }}
                            >
                                {t('change_password')}
                            </Button>
                        </div>
                    </div>
                {/if}
            </div>
            {#if role === 'Admin'}
                <Checkbox class="flex items-center gap-2" bind:checked={formData.is_active} bind:value={formData.is_active}>
                    Active Status
                </Checkbox>
            {/if}
            <input type="hidden" name="is_active" value={formData.is_active}>
        </form>
        <div class="flex gap-2 mt-6 items-center">
            <Button type="submit" color="blue" on:click={() => editForm.requestSubmit()} disabled={isSubmitting}>
                {#if isSubmitting}
                    {t('saving')}
                {:else}
                    {t('confirm')}
                {/if}
            </Button>
            <Button color="alternative" on:click={() => editModalOpen = false}>Cancel</Button>
        </div>
    </div>
</Modal>


<Modal bind:open={passwordModalOpen} size="xs" outsideclose>
    <div class="text-2xl font-semibold mb-4 text-black">{t('change_password')}</div>
    <div class="p-2">
        <form
            bind:this={passwordForm}
            action="?/force_change_password"
            method="POST"
            use:enhance={handlePasswordEnhance}
            class="space-y-3"
        >
            <input type="hidden" name="user_id" value={user.id}>
            <div class="grid gap-2">
                <Label for="new_password" class="space-y-2 text-left text-sm">{t('new_password')}</Label>
                <Input 
                    id="new_password"
                    name="new_password"
                    type="password" 
                    bind:value={passwordFormData.new_password}
                    placeholder="Enter new password" 
                    required 
                    on:input={() => passwordFieldsEverTyped = true}
                />
            </div>
            <div class="grid gap-2">
                <Label for="confirm_password" class="space-y-2 text-left text-sm">{t('confirm_password')}</Label>
                <Input 
                    id="confirm_password"
                    name="confirm_password"
                    type="password" 
                    bind:value={passwordFormData.confirm_password}
                    placeholder="Confirm new password" 
                    required 
                    on:input={() => passwordFieldsEverTyped = true}
                />
                <div style="min-height:1em;" class="flex justify-left items-top">
                    {#if passwordFieldsEverTyped && !passwordsMatch && passwordFormData.confirm_password.length > 0}
                        <span class="text-xs text-red-600 text-left">Passwords do not match.</span>
                    {/if}
                </div>
            </div>
            <div class="flex gap-2 mt-6 items-center">
                <Button type="submit" color="blue" disabled={!passwordsMatch || isPasswordSubmitting}>
                    {#if isPasswordSubmitting}
                        {t('saving')}
                    {:else}
                        {t('confirm')}
                    {/if}
                </Button>
                <Button color="alternative"
                    on:click={() => {
                        passwordModalOpen = false;
                    }}
                >
                    {t('cancel')}
                </Button>
            </div>
        </form>
    </div>
</Modal>