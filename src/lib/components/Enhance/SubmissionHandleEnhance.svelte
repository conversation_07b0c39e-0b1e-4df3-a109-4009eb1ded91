<script lang="ts" context="module">
    interface EnhanceOptions {
        modalOpen: boolean;
        setModalOpen?: (value: boolean) => void;
        setPending?: (value: boolean) => void;
        setShowSuccessMessage?: (value: boolean) => void;
        setSuccessMessage?: (value: string) => void;
        setShowErrorMessage?: (value: boolean) => void;
        setErrorMessage?: (value: string) => void;
    }

    export const handleEnhance = (options: EnhanceOptions) => {
        return async ({ result, update }: { result: any; update: () => Promise<void> }) => {
            
            // TODO - Delete this
            // console.log(`src\lib\components\Enhance\SubmissionHandleEnhance.svelte's result - ${JSON.stringify(result)}`)
            // console.log(`src\lib\components\Enhance\SubmissionHandleEnhance.svelte's result type - ${result.type}`)

            // Set pending state if the option is provided
            options.setPending?.(true);

            if (result.type === 'failure') {
                options.setShowErrorMessage(true);
                options.setErrorMessage(result.data?.error || 'Status : Operation failed');
                // Don't close modal on error
            } else if (result.type === 'success') {
                options.setShowSuccessMessage(true);
                options.setSuccessMessage(result.data.res_msg || 'Status : Operation success');
            }
            // Update the page data
            await update();
            
            // Reset pending state if the option is provided
            options.setPending?.(false);
        };
    };
</script>