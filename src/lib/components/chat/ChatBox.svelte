<!-- Chatbox.svelte -->
<script lang="ts">
    import { t } from '$lib/stores/i18n';
	import { onMount } from 'svelte';
	import { chatStore, messages, connectionStatus, type MessageStatus } from '$lib/stores/chatStore';
	import { chatService } from '$lib/api/features/chat/chatService';
	import ChatConnection from './ChatConnection.svelte';
	import ChatMessage from './ChatMessage.svelte';
	import ChatInput from './ChatInput.svelte';
	import { string } from 'zod';

	export let ticketId: string;
	export let access_token: string;
	export let height: string = '100%';
	export let showHeader: boolean = true;

	export let ticket: any;
	export let users: any[];
	export let priorities: any[];
	export let statuses: any[];

    export let loginUser: any;
	export let ownerUsername: string;
	export let loginUsername: string;
    export let status_id : number;
    export let ticket_topics: any[];

	let chatConnection: ChatConnection;
	let messagesContainer: HTMLDivElement;
	let isLoading = false;
	let ticketDetails = null;

	const displayDate = (timestamp) => {
		const displayCreated = new Date(timestamp);

		return displayCreated.toLocaleDateString('en-GB', {
			day: '2-digit',
			month: 'short',
			year: 'numeric'
		});
	};

	// TODO - Delete this
	// console.log(`src/lib/components/chat/ChatBox.svelte's ticketId - ${ticketId}`)
	// console.log(`src/lib/components/chat/ChatBox.svelte's users - ${users}`)
	// console.log(`src/lib/components/chat/ChatBox.svelte's priorities - ${priorities}`)
	// // Auto-scroll to bottom when messages change
	// $: if (messagesContainer && $messages) {
	//   setTimeout(() => {
	//     messagesContainer.scrollTop = messagesContainer.scrollHeight;
	//   }, 0);
	// }

	onMount(async () => {
		loadTicketDetails();
	});

	async function loadTicketDetails() {
		try {
			isLoading = true;
			ticketDetails = await chatService.getTicketDetails(ticketId);
		} catch (error) {
			console.error('Error loading ticket details:', error);
			chatStore.setError('Failed to load ticket details');
		} finally {
			isLoading = false;
		}
	}

	// // Send a message
	// function handleSend(event: CustomEvent<{ message: string, type: string }>) {
	//   const { message, type } = event.detail;

	//   // TODO - Delete this
	//   console.log(`src\lib\components\chat\ChatBox.svelte's message - ${message}`)
	//   console.log(`src\lib\components\chat\ChatBox.svelte's message type - ${type}`)

	//   chatConnection.sendMessage(message, type);
	// }

	// Send a message
	async function handleSend(
		event: CustomEvent<{ access_token: string; ticketId: string; message: string; type: string }>
	) {
		const { message, type } = event.detail;

		// TODO - Delete this
		// console.log(`src/lib/components/chat/ChatBox.svelte's ticketId - ${ticketId}`);
		// console.log(`src/lib/components/chat/ChatBox.svelte's message - ${message}`);
		// console.log(`src/lib/components/chat/ChatBox.svelte's message type - ${type}`);

		//   chatConnection.sendMessage(message, type);

		try {
			// First create the message through the API
			const messageResponse = await chatService.sendMessage(access_token, ticketId, message, type);

			// TODO - Delete this
			console.log(
				`src/lib/components/chat/ChatBox.svelte's messageResponse - ${JSON.stringify(messageResponse)}`
			);

			// Second, send a message to the group
			const sourceOfMessage = 'from_webapp';
			chatConnection.sendMessage(
				message,
				type,
				sourceOfMessage,
				messageResponse.user_name,
				messageResponse.is_self,
				messageResponse.id
			);

			// Now we have a proper message ID and is_self value from the server

			// Add message to local store immediately for better UX
			chatStore.addMessage({
				id: messageResponse.id,
				// message: message,
				message: messageResponse.message,
				user_name: messageResponse.user_name,
				is_self: messageResponse.is_self,
				// message_type: type,
				message_type: messageResponse.message_type,
				status: 'SENT',
				created_on: messageResponse.created_on,
				file_url: null
			});

			// WebSocket will handle the rest (delivery confirmation, etc.)
		} catch (error) {
			console.error('Error sending message:', error);
			chatStore.setError('Failed to send message. Please try again.');
		}
	}

	// Handle file uploads
	async function handleFileSelected(event: CustomEvent<File>) {
		const file = event.detail;

		if (!file) return;

		// Determine message type based on file type
		const isImage = file.type.startsWith('image/');
		const messageType = isImage ? 'IMAGE' : 'FILE';

		try {
			// Upload the file using service
			const fileUploadResponse = await chatService.uploadFile(ticketId, file);

			// Send message with file reference
			chatConnection.sendMessage(isImage ? '[Image]' : `[File: ${file.name}]`, messageType);
		} catch (error) {
			console.error('Error uploading file:', error);
			chatStore.setError('Failed to upload file. Please try again.');
		}
	}

	// Update message status
	async function updateMessageStatus(messageId: string, newStatus: MessageStatus) {
		try {
			await chatService.updateMessageStatus(messageId, newStatus);

			// Update in local store
			chatStore.updateMessageStatus(messageId, newStatus);
		} catch (error) {
			console.error('Error updating message status:', error);
		}
	}

	// messages.subscribe((value) => {
	// 	console.log('Subscribed value:', value);
	// 	return () => console.log('Unsubscribed from store');
	// });
	// console.log(messages);

    import { Heading, Button, Dropdown, DropdownItem } from 'flowbite-svelte';
    import { DotsVerticalOutline, UserSolid, CaretLeftSolid, CaretRightSolid } from 'flowbite-svelte-icons';
    import TransferTicketOwner from '$lib/components/UI/TransferTicketOwner.svelte';
    import ChangeTicketStatus from '$lib/components/UI/ChangeTicketStatus.svelte';
    import ChangeTicketPriority from '$lib/components/UI/ChangeTicketPriority.svelte';
</script>

<div
    class="chat-container flex flex-col overflow-hidden border bg-gray-50 shadow-sm h-full"
    style={`height:${height};`} 
>
	{#if showHeader}
		<!--Chat Header-->
		<div class="chat-header flex items-center justify-between bg-sky-600 p-5 text-white">
			<!--Left : Title -->
			<div class="flex items-center">
                <img
                    src={ticket.customer.picture_url}
                    alt="Customer Profile Picture"
                    class="h-10 w-10 rounded-full border-2 border-gray-400 shadow-md mr-2"
                />
                {ticket?.customer.name}
			</div>

			<!--Right : Connection -->
			<div class="ml-auto flex items-center space-x-3">
				<div
                    size="xs"
                    class="connection-status rounded-md px-2 py-2 text-xs {$connectionStatus === 'connected'
                        ? 'bg-green-500'
                        : 'bg-red-500'}"
                    >
                    {t($connectionStatus === 'connected' ? 'connected' : 'disconnected')}
                </div>

			</div>

            <div class="flex items-center justify-end gap-2 pr-4">      
                <Button color="none" class="p-1 ">
                    <DotsVerticalOutline class="h-8 w-8" />
                </Button>
                <Dropdown class="flex flex-col text-left">
                    <TransferTicketOwner
                        {ticket}
                        {users}
                        loggedInUsername={loginUser.username}
                        loggedInRole={loginUser.roles[0].name}
                        isDropDownItem={true}
                    />
                    <ChangeTicketStatus {ticket} {statuses} {ticket_topics} isDropDownItem={true} />
                    <ChangeTicketPriority {ticket} {priorities} isDropDownItem={true} />
                </Dropdown>
            </div>
		</div>
	{/if}

	{#if $chatStore.error}
		<div class="error-message border-b border-red-200 bg-red-100 p-2 text-sm text-red-700">
			{$chatStore.error}
			<button class="ml-2 text-red-500 hover:text-red-700" on:click={() => chatStore.setError(null)}
				>&times;</button
			>
		</div>
	{/if}

    <div
        bind:this={messagesContainer}
        class="messages-container flex-1 space-y-4 overflow-y-auto p-4 min-h-[300px]"
    >
		{#if isLoading}
			<div class="flex h-full items-center justify-center">
				<div class="h-8 w-8 animate-spin rounded-full border-b-2 border-blue-500"></div>
			</div>
		{:else if $messages.length === 0}
			<div class="flex h-full items-center justify-center text-gray-400">No messages yet</div>
		{:else}
			{#each $messages as message, index (message.id)}
				{#if index === 0 || displayDate(message.created_on) !== displayDate($messages[index - 1].created_on)}
					<div
						class="mx-auto my-2 w-40 rounded-md bg-neutral-700 p-2 text-center text-sm text-gray-50"
					>
						{displayDate(message.created_on)}
					</div>
				{/if}
				<ChatMessage
					id={message.id}
					message={message.message}
					userName={message.user_name}
					isSelf={message.is_self}
					status={message.status}
					timestamp={message.created_on}
					messageType={message.message_type}
					fileUrl={message.file_url}
					onUpdateStatus={updateMessageStatus}
                    message_intents={message.message_intents}
                    sub_message_intents={message.sub_message_intents}
				/>
			{/each}
		{/if}
	</div>

    <!-- 3 is status id of 'closed' status -->
	<ChatInput
		disabled={$connectionStatus !== 'connected' || status_id === 3} 
		on:send={handleSend}
		on:fileSelected={handleFileSelected}
		{ownerUsername}
		{loginUsername}
	/>
	<ChatConnection bind:this={chatConnection} {access_token} {ticketId} autoConnect={true} />
</div>
