<script lang="ts">
    import { createEventDispatcher, onMount, onDestroy } from 'svelte';
    import LoadingSpinner from '../common/LoadingSpinner.svelte';
    import InfiniteScroll from '../common/InfiniteScroll.svelte';
    import type { CustomerPlatformIdentity, Message } from '$lib/types/customer';
    import { getBackendUrl } from '$src/lib/config';
    import { platformWebSocket } from '$lib/websocket/platformWebSocket';
    
    export let platformIdentities: CustomerPlatformIdentity[] = [];
    export let selectedPlatformId: number | null = null;
    export let hasMore: boolean = false;
    
    const dispatch = createEventDispatcher();
    
    let searchTerm = '';
    let latestMessages: Map<number, Message> = new Map();
    let unreadCounts: Map<number, number> = new Map();
    let loadingMore = false;
    
    $: filteredIdentities = filterIdentities(platformIdentities, searchTerm);
    $: sortedIdentities = sortIdentities(filteredIdentities, latestMessages);
    
    onMount(() => {
        loadAdditionalData();
        
        // Only access window in browser environment
        if (typeof window !== 'undefined') {
            // Listen for WebSocket events
            window.addEventListener('platform-new-message', handleNewMessage);
            window.addEventListener('platform-status-update', handleStatusUpdate);
            window.addEventListener('platform-typing', handleTypingIndicator);
            window.addEventListener('platform-bulk-update', handleBulkUpdate);
            
            // Subscribe to all visible platforms for real-time updates
            const visiblePlatformIds = platformIdentities.map(p => p.id);
            if (visiblePlatformIds.length > 0) {
                platformWebSocket.subscribeToMultiplePlatforms(visiblePlatformIds);
            }
        }
    });
    
    onDestroy(() => {
        // Only access window in browser environment
        if (typeof window !== 'undefined') {
            // Clean up event listeners
            window.removeEventListener('platform-new-message', handleNewMessage);
            window.removeEventListener('platform-status-update', handleStatusUpdate);
            window.removeEventListener('platform-typing', handleTypingIndicator);
            window.removeEventListener('platform-bulk-update', handleBulkUpdate);
        }
    });
    
    async function loadAdditionalData() {
        // Load latest messages and unread counts for all platform identities
        const platformIds = platformIdentities.map(p => p.id);
        if (platformIds.length > 0) {
            await Promise.all([
                loadLatestMessages(platformIds),
                loadUnreadCounts(platformIds)
            ]);
        }
    }
    
    async function loadLatestMessages(platformIds: number[]) {
        try {
            // Batch load latest messages
            const response = await fetch(
                `${getBackendUrl()}/customer/api/platform-messages/?platform_ids=${platformIds.join(',')}`,
                { credentials: 'include' }
            );
            
            if (response.ok) {
                const data = await response.json();
                latestMessages = new Map(Object.entries(data).map(([k, v]) => [parseInt(k), v as Message]));
            }
        } catch (error) {
            console.error('Error loading latest messages:', error);
        }
    }
    
    async function loadUnreadCounts(platformIds: number[]) {
        try {
            // Batch load unread counts
            const response = await fetch(
                `${getBackendUrl()}/customer/api/platform-unread-counts/?platform_ids=${platformIds.join(',')}`,
                { credentials: 'include' }
            );
            
            if (response.ok) {
                const data = await response.json();
                unreadCounts = new Map(Object.entries(data).map(([k, v]) => [parseInt(k), v as number]));
            }
        } catch (error) {
            console.error('Error loading unread counts:', error);
        }
    }
    
    function filterIdentities(identities: CustomerPlatformIdentity[], search: string) {
        if (!search) return identities;
        
        const searchLower = search.toLowerCase();
        return identities.filter(p => 
            p.display_name?.toLowerCase().includes(searchLower) ||
            p.channel_name?.toLowerCase().includes(searchLower) ||
            p.platform.toLowerCase().includes(searchLower) ||
            p.platform_user_id?.toLowerCase().includes(searchLower) ||
            (typeof p.customer === 'object' && p.customer.name?.toLowerCase().includes(searchLower)) ||
            (typeof p.customer === 'object' && p.customer.email?.toLowerCase().includes(searchLower))
        );
    }
    
    function sortIdentities(identities: CustomerPlatformIdentity[], messages: Map<number, Message>) {
        return [...identities].sort((a, b) => {
            const aMsg = messages.get(a.id);
            const bMsg = messages.get(b.id);
            
            // If both have messages, sort by message time
            if (aMsg && bMsg) {
                return new Date(bMsg.created_on).getTime() - new Date(aMsg.created_on).getTime();
            }
            
            // If only one has a message, it goes first
            if (aMsg && !bMsg) return -1;
            if (!aMsg && bMsg) return 1;
            
            // If neither has messages, sort by last interaction
            const aTime = a.last_interaction || a.created_on;
            const bTime = b.last_interaction || b.created_on;
            return new Date(bTime).getTime() - new Date(aTime).getTime();
        });
    }
    
    function handleIdentityClick(identity: CustomerPlatformIdentity) {
        // Check if customer is just an ID or a full object
        let customerId: number;
        
        if (typeof identity.customer === 'number') {
            // If customer is just an ID, use it directly
            customerId = identity.customer;
        } else if (identity.customer && typeof identity.customer === 'object' && identity.customer.customer_id) {
            // If customer is an object with customer_id
            customerId = identity.customer.customer_id;
        } else {
            console.error('Platform identity missing valid customer data:', identity);
            return;
        }
        
        dispatch('select', {
            platformId: identity.id,
            customerId: customerId
        });
    }
    
    function handleLoadMore() {
        if (!loadingMore && hasMore) {
            loadingMore = true;
            dispatch('loadMore');
            loadingMore = false;
        }
    }
    
    function handleNewMessage(event: CustomEvent) {
        const { platformId, message, unreadCount } = event.detail;
        
        // Update latest message
        latestMessages.set(platformId, message);
        latestMessages = latestMessages; // Trigger reactivity
        
        // Update unread count
        unreadCounts.set(platformId, unreadCount);
        unreadCounts = unreadCounts; // Trigger reactivity
        
        // Re-sort the list to move updated platform to top
        sortedIdentities = sortIdentities(filteredIdentities, latestMessages);
    }
    
    function handleStatusUpdate(event: CustomEvent) {
        const { platformId, status } = event.detail;
        
        // Update platform status if needed
        const platform = platformIdentities.find(p => p.id === platformId);
        if (platform) {
            // You can add status to your platform type if needed
            // platform.status = status;
            platformIdentities = platformIdentities; // Trigger reactivity
        }
    }
    
    function handleTypingIndicator(event: CustomEvent) {
        const { platformId, isTyping, userName } = event.detail;
        // You can implement typing indicator UI here if needed
    }
    
    function handleBulkUpdate(event: CustomEvent) {
        const updates = event.detail;
        
        // Update multiple platforms at once
        Object.entries(updates).forEach(([platformId, data]: [string, any]) => {
            const id = parseInt(platformId);
            
            if (data.latest_message) {
                latestMessages.set(id, data.latest_message);
            }
            
            if (data.unread_count !== undefined) {
                unreadCounts.set(id, data.unread_count);
            }
        });
        
        // Trigger reactivity
        latestMessages = latestMessages;
        unreadCounts = unreadCounts;
        sortedIdentities = sortIdentities(filteredIdentities, latestMessages);
    }
    
    function formatTime(dateString: string): string {
        const date = new Date(dateString);
        const now = new Date();
        const diff = now.getTime() - date.getTime();
        
        // Less than a minute
        if (diff < 60000) return 'Just now';
        
        // Less than an hour
        if (diff < 3600000) {
            const minutes = Math.floor(diff / 60000);
            return `${minutes}m ago`;
        }
        
        // Less than a day
        if (diff < 86400000) {
            const hours = Math.floor(diff / 3600000);
            return `${hours}h ago`;
        }
        
        // Less than a week
        if (diff < 604800000) {
            const days = Math.floor(diff / 86400000);
            return `${days}d ago`;
        }
        
        // Format as date
        return date.toLocaleDateString();
    }
    
    function getPlatformColor(platform: string): string {
        const colors = {
            'LINE': 'bg-green-100 text-green-700',
            'WHATSAPP': 'bg-green-100 text-green-700',
            'FACEBOOK': 'bg-blue-100 text-blue-700',
            'INSTAGRAM': 'bg-purple-100 text-purple-700',
            'TELEGRAM': 'bg-sky-100 text-sky-700'
        };
        return colors[platform] || 'bg-gray-100 text-gray-700';
    }
    
    function getCustomerName(identity: CustomerPlatformIdentity): string {
        if (typeof identity.customer === 'object') {
            return identity.customer.name || identity.customer.email || 'Unknown Customer';
        } else {
            return `Customer #${identity.customer}`;
        }
    }
</script>

<div class="h-full flex flex-col">
    <!-- Header with Search -->
    <div class="p-4 border-b border-gray-200">
        <h2 class="text-lg font-semibold mb-3">All Conversations</h2>
        <div class="relative">
            <input
                type="text"
                bind:value={searchTerm}
                placeholder="Search by name, channel, or customer..."
                class="w-full px-3 py-2 pl-9 text-sm border border-gray-300 rounded-lg 
                       focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <svg class="absolute left-3 top-2.5 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
        </div>
    </div>
    
    <!-- Platform Identity List -->
    <div class="flex-1 overflow-y-auto custom-scrollbar">
        {#if sortedIdentities.length === 0}
            <div class="text-center text-gray-500 p-8">
                {searchTerm ? 'No conversations found' : 'No active conversations'}
            </div>
        {:else}
            <div class="divide-y divide-gray-100">
                {#each sortedIdentities as identity (identity.id)}
                    <button
                        class="w-full p-4 hover:bg-gray-50 transition-colors text-left relative
                               {selectedPlatformId === identity.id ? 'bg-blue-50 border-l-4 border-blue-500' : ''}"
                        on:click={() => handleIdentityClick(identity)}
                    >
                        <div class="flex items-start justify-between">
                            <div class="flex-1 min-w-0 pr-2">
                                <!-- Customer and Platform Info -->
                                <div class="flex items-center gap-2 mb-1">
                                    <span class="font-medium text-gray-900 truncate">
                                        {identity.display_name || identity.platform_username || identity.platform_user_id}
                                    </span>
                                    <span class="text-xs px-2 py-0.5 rounded-full {getPlatformColor(identity.platform)}">
                                        {identity.platform}
                                    </span>
                                </div>
                                
                                <!-- Customer Name -->
                                <div class="text-sm text-gray-600 truncate">
                                    {getCustomerName(identity)}
                                </div>
                                
                                <!-- Latest Message Preview -->
                                {#if latestMessages.has(identity.id)}
                                    {@const message = latestMessages.get(identity.id)}
                                    <div class="text-sm text-gray-500 truncate mt-1">
                                        {message.is_self ? 'You: ' : ''}{message.message}
                                    </div>
                                {:else if identity.last_message}
                                    <div class="text-sm text-gray-500 truncate mt-1">
                                        {identity.last_message}
                                    </div>
                                {/if}
                            </div>
                            
                            <!-- Right Side Info -->
                            <div class="ml-2 flex flex-col items-end">
                                <!-- Time -->
                                {#if latestMessages.has(identity.id)}
                                    {@const message = latestMessages.get(identity.id)}
                                    <span class="text-xs text-gray-500 whitespace-nowrap">
                                        {formatTime(message.created_on)}
                                    </span>
                                {:else if identity.last_message_time}
                                    <span class="text-xs text-gray-500 whitespace-nowrap">
                                        {formatTime(identity.last_message_time)}
                                    </span>
                                {/if}
                                
                                <!-- Unread Count -->
                                {#if unreadCounts.get(identity.id) > 0}
                                    <span class="mt-1 inline-flex items-center justify-center min-w-[20px] h-5 px-1.5
                                                 text-xs font-bold text-white bg-red-500 rounded-full">
                                        {unreadCounts.get(identity.id)}
                                    </span>
                                {:else if identity.unread_count > 0}
                                    <span class="mt-1 inline-flex items-center justify-center min-w-[20px] h-5 px-1.5
                                                 text-xs font-bold text-white bg-red-500 rounded-full">
                                        {identity.unread_count}
                                    </span>
                                {/if}
                            </div>
                        </div>
                    </button>
                {/each}
            </div>
            
            <!-- Load More -->
            {#if hasMore}
                <InfiniteScroll
                    on:loadMore={handleLoadMore}
                    loading={loadingMore}
                />
            {/if}
        {/if}
    </div>
</div>

<style>
    .custom-scrollbar {
        scrollbar-width: thin;
        scrollbar-color: #e5e7eb #f9fafb;
    }
    
    .custom-scrollbar::-webkit-scrollbar {
        width: 6px;
    }
    
    .custom-scrollbar::-webkit-scrollbar-track {
        background: #f9fafb;
    }
    
    .custom-scrollbar::-webkit-scrollbar-thumb {
        background-color: #e5e7eb;
        border-radius: 3px;
    }
    
    .custom-scrollbar::-webkit-scrollbar-thumb:hover {
        background-color: #d1d5db;
    }
</style>