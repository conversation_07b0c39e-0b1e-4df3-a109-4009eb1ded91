<script lang="ts">
	import { t } from '$lib/stores/i18n';
	import type { Customer } from '$lib/types/customer';
	import { 
		Card, 
		Button, 
		Label, 
		Input, 
		Checkbox,
		AccordionItem,
		Hr,
		Dropdown,
		DropdownItem,
		Textarea
	} from "flowbite-svelte";
	import { getBackendUrl } from '$src/lib/config';

	export let customer: Customer;
	function formatDate(dateString: string | null) {
		if (!dateString) return 'N/A';
		return new Date(dateString).toLocaleDateString();
	}
	
	function getCustomerTypeColor(type: string) {
		switch (type) {
			case 'VIP': return 'bg-purple-100 text-purple-800';
			case 'REGULAR': return 'bg-blue-100 text-blue-800';
			case 'NEW': return 'bg-green-100 text-green-800';
			default: return 'bg-gray-100 text-gray-800';
		}
	}
	
</script>

<div class="w-full h-full p-4 space-y-6 overflow-y-auto">
	<!-- Customer Profile Card -->
	<div class="w-full p-6 mb-6 bg-white rounded-lg shadow-md">
		<!-- Customer Header -->
		<div class="text-center">
			<div class="w-20 h-20 bg-gray-200 rounded-full mx-auto mb-3 flex items-center justify-center text-2xl font-medium text-gray-600">
				{customer.name?.charAt(0) || 'C'}
			</div>
			<h2 class="text-xl font-semibold">{customer.name || 'Unknown Customer'}</h2>
			<p class="text-sm text-gray-500">CUS-{customer.customer_id.toString().padStart(3, '0')}-2025</p>
		</div>
		
		<!-- Basic Information -->
		<div>
			<div class="text-lg font-medium text-gray-700">Basic Information</div>
			<div class="space-y-3">
				<div>
					<label class="text-xs text-gray-500">Full Name</label>
					<p class="text-sm font-medium">
						{customer.first_name && customer.last_name ? `${customer.first_name} ${customer.last_name}`: 'Not provided'}
					</p>
				</div>
				
				<div>
					<label class="text-xs text-gray-500">Age</label>
					<p class="text-sm font-medium">{customer.age || 'Not provided'}</p>
				</div>
				
				<div>
					<label class="text-xs text-gray-500">Customer Type</label>
					<p class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {getCustomerTypeColor(customer.customer_type)}">
						{customer.customer_type}
					</p>
				</div>
			</div>
		</div>
		
		<!-- Contact Information -->
		<div>
			<!-- <h3 class="text-sm font-medium text-gray-700 mb-3">Contact Information</h3> -->
			<div class="space-y-3">
				<div>
					<label class="text-xs text-gray-500">Phone Number</label>
					<p class="text-sm font-medium">{customer.phone || 'Not provided'}</p>
				</div>
				
				<div>
					<label class="text-xs text-gray-500">Email</label>
					<p class="text-sm font-medium">{customer.email || 'Not provided'}</p>
				</div>
				
				<div>
					<label class="text-xs text-gray-500">Address</label>
					<p class="text-sm font-medium">
						{customer.address_line1 || 'Not provided'}
						{#if customer.address_line2}
							<br>{customer.address_line2}
						{/if}
						{#if customer.district || customer.province}
							<br>{[customer.district, customer.province].filter(Boolean).join(', ')}
						{/if}
					</p>
				</div>
				
				<div>
					<label class="text-xs text-gray-500">Country</label>
					<p class="text-sm font-medium">{customer.country || 'Thailand'}</p>
				</div>
			</div>
		</div>
		
		<!-- Organization -->
		<div>
			<!-- <h3 class="text-sm font-medium text-gray-700 mb-3">Organization</h3> -->
			<div class="space-y-3">
				<div>
					<label class="text-xs text-gray-500">Company</label>
					<p class="text-sm font-medium">{customer.company_name || 'Not provided'}</p>
				</div>
				
				<div>
					<label class="text-xs text-gray-500">Contact Channel</label>
					<p class="text-sm font-medium">{customer.main_interface_id?.name || 'LINE'}</p>
				</div>
			</div>
		</div>
		
		<!-- Platform Identities -->
		<div>
			<h3 class="text-sm font-medium text-gray-700 mb-3">Connected Platforms</h3>
			<div class="space-y-2">
				{#if customer.platforms && customer.platforms.length > 0}
					{#each customer.platforms as platform}
						<div class="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
							<div class="flex items-center space-x-2">
								<span class="text-lg">{platform.platform === 'LINE' ? '💚' : '💬'}</span>
								<span class="text-sm font-medium">{platform.platform}</span>
							</div>
							{#if platform.verified}
								<svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
									<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
								</svg>
							{/if}
						</div>
					{/each}
				{:else}
					<p class="text-sm text-gray-500">No connected platforms</p>
				{/if}
			</div>
		</div>
		
		<!-- Tags -->
		<!-- {#if customer.tags && customer.tags.length > 0}
			<div>
				<h3 class="text-sm font-medium text-gray-700 mb-3">Tags</h3>
				<div class="flex flex-wrap gap-2">
					{#each customer.tags as tag}
						<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
							{tag}
						</span>
					{/each}
				</div>
			</div>
		{/if} -->
	</div>

	<!-- Customer Tags -->
	<div class="w-full p-6 mb-6 bg-white rounded-lg shadow-md"> 
		<div class="text-lg font-medium text-gray-700">Customer Tags</div>
	</div>
	
	<!-- Staff History -->
	<div class="w-full p-6 mb-6 bg-white rounded-lg shadow-md"> 
		<div class="text-lg font-medium text-gray-700">Staff History</div>
	</div>
	
	<!-- Notes -->
	<div class="w-full p-6 mb-6 bg-white rounded-lg shadow-md"> 
		<div class="text-lg font-medium text-gray-700">Notes</div>
	</div>
</div>