<script lang="ts">
    import { onMount, onDestroy, afterUpdate } from 'svelte';
    import { platformChannelStore, selectedChannel } from '$lib/stores/platformChannelStore';
    import { CustomerWebSocketManager } from '$lib/websocket/CustomerWebSocketManager';
    import type { Message } from '$lib/types/customer';
    import { format } from 'date-fns';
    import { th } from 'date-fns/locale';
    import { getBackendUrl } from '$src/lib/config';
    
    export let customerId: number;
    export let platformId: number;
    
    let messageContainer: HTMLDivElement;
    let messageInput = '';
    let isLoading = false;
    let isLoadingMore = false;
    let isSending = false;
    let wsManager: CustomerWebSocketManager;
    let shouldScrollToBottom = true;
    let lastScrollTop = 0;
    
    $: channel = $selectedChannel;
    $: messages = channel?.messages || [];
    
    onMount(async () => {
        wsManager = CustomerWebSocketManager.getInstance();
        
        // Subscribe to platform channel
        await wsManager.subscribeToPlatform(customerId, platformId);
        
        // Register message handlers
        wsManager.on('platform_message', handleNewMessage);
        wsManager.on('message_status_update', handleMessageStatusUpdate);
        wsManager.on('typing_indicator', handleTypingIndicator);
        
        // Load initial messages
        await loadInitialMessages();
    });
    
    onDestroy(() => {
        // Unsubscribe from platform
        if (wsManager) {
            wsManager.unsubscribePlatform(platformId);
            wsManager.off('platform_message', handleNewMessage);
            wsManager.off('message_status_update', handleMessageStatusUpdate);
            wsManager.off('typing_indicator', handleTypingIndicator);
        }
    });
    
    afterUpdate(() => {
        // Auto-scroll to bottom when new messages arrive
        if (shouldScrollToBottom && messageContainer) {
            messageContainer.scrollTop = messageContainer.scrollHeight;
        }
    });
    
    async function loadInitialMessages() {
        platformChannelStore.setLoading(platformId, true);
        
        try {
            const response = await fetch(
                `${getBackendUrl()}/customer/api/customers/${customerId}/platforms/${platformId}/messages/?limit=50`,
                // {
                //     headers: {
                //         // 'Authorization': `Bearer ${localStorage.getItem('auth-token')}`,
                //         'Content-Type': 'application/json'
                //     }
                // }
            );
            
            if (response.ok) {
                const data = await response.json();
                platformChannelStore.setMessages(platformId, data.messages, data.has_more);
                
                // Mark messages as read
                const unreadIds = data.messages
                    .filter((m: Message) => !m.is_self && m.status !== 'READ')
                    .map((m: Message) => m.id);
                
                if (unreadIds.length > 0) {
                    markMessagesAsRead(unreadIds);
                }
            } else {
                const errorData = await response.json();
                throw new Error(errorData.error || 'Failed to load messages');
            }
        } catch (error) {
            console.error('Error loading messages:', error);
            platformChannelStore.setError(platformId, error.message || 'Failed to load messages');
        } finally {
            platformChannelStore.setLoading(platformId, false);
        }
    }
    
    async function loadMoreMessages() {
        if (!channel || isLoadingMore || !channel.hasMore || messages.length === 0) {
            return;
        }
        
        isLoadingMore = true;
        const oldestMessage = messages[0];
        const scrollHeight = messageContainer.scrollHeight;
        
        try {
            const response = await fetch(
                `${getBackendUrl()}/customer/api/customers/${customerId}/platforms/${platformId}/messages/?` +
                `before_message_id=${oldestMessage.id}&limit=50`,
                {
                    headers: {
                        // 'Authorization': `Bearer ${localStorage.getItem('auth-token')}`,
                        'Content-Type': 'application/json'
                    }
                }
            );
            
            if (response.ok) {
                const data = await response.json();
                platformChannelStore.prependMessages(platformId, data.messages, data.has_more);
                
                // Maintain scroll position
                requestAnimationFrame(() => {
                    if (messageContainer) {
                        const newScrollHeight = messageContainer.scrollHeight;
                        messageContainer.scrollTop = newScrollHeight - scrollHeight;
                    }
                });
            } else {
                const errorData = await response.json();
                throw new Error(errorData.error || 'Failed to load more messages');
            }
        } catch (error) {
            console.error('Error loading more messages:', error);
            platformChannelStore.setError(platformId, error.message || 'Failed to load more messages');
        } finally {
            isLoadingMore = false;
        }
    }
    
    function handleNewMessage(customerId: number, data: any) {
        if (data.message && data.message.platform_identity_id === platformId) {
            platformChannelStore.addMessage(platformId, data.message);
            shouldScrollToBottom = true;
            
            // Mark as read if we're viewing this channel
            if (!data.message.is_self && data.message.status !== 'READ') {
                markMessagesAsRead([data.message.id]);
            }
        }
    }
    
    function handleMessageStatusUpdate(customerId: number, data: any) {
        platformChannelStore.updateMessageStatus(platformId, data.message_id, data.status);
    }
    
    function handleTypingIndicator(customerId: number, data: any) {
        if (data.platform_identity_id === platformId) {
            platformChannelStore.setTypingIndicator(platformId, data.is_typing);
        }
    }
    
    async function sendMessage() {
        if (!messageInput.trim() || isSending) return;
        
        const content = messageInput.trim();
        messageInput = '';
        isSending = true;
        
        try {
            // Send via API instead of WebSocket
            const response = await fetch(
                `${getBackendUrl()}/customer/api/customers/${customerId}/platforms/${platformId}/send/`,
                {
                    method: 'POST',
                    headers: {
                        // 'Authorization': `Bearer ${localStorage.getItem('auth-token')}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        message: content,
                        message_type: 'TEXT'
                    })
                }
            );
            
            if (response.ok) {
                const newMessage = await response.json();
                // Add message to store
                platformChannelStore.addMessage(platformId, newMessage);
                shouldScrollToBottom = true;
            } else {
                const errorData = await response.json();
                throw new Error(errorData.error || 'Failed to send message');
            }
        } catch (error) {
            console.error('Error sending message:', error);
            // Restore message input on error
            messageInput = content;
            // Show error to user
            alert(error.message || 'Failed to send message. Please try again.');
        } finally {
            isSending = false;
        }
    }
    
    async function markMessagesAsRead(messageIds: number[]) {
        try {
            const response = await fetch(
                `${getBackendUrl()}/customer/api/customers/${customerId}/platforms/${platformId}/mark-read/`,
                {
                    method: 'POST',
                    headers: {
                        // 'Authorization': `Bearer ${localStorage.getItem('auth-token')}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        message_ids: messageIds
                    })
                }
            );
            
            if (response.ok) {
                const result = await response.json();
                // Update message statuses in store
                messageIds.forEach(id => {
                    platformChannelStore.updateMessageStatus(platformId, id, 'READ');
                });
            }
        } catch (error) {
            console.error('Error marking messages as read:', error);
        }
    }
    
    function handleScroll(event: Event) {
        const target = event.target as HTMLDivElement;
        const scrollTop = target.scrollTop;
        const scrollHeight = target.scrollHeight;
        const clientHeight = target.clientHeight;
        
        // Check if scrolled to top for loading more
        if (scrollTop < 100 && scrollTop < lastScrollTop) {
            loadMoreMessages();
        }
        
        // Update auto-scroll behavior
        shouldScrollToBottom = scrollHeight - scrollTop - clientHeight < 100;
        lastScrollTop = scrollTop;
    }
    
    function handleKeyPress(event: KeyboardEvent) {
        if (event.key === 'Enter' && !event.shiftKey) {
            event.preventDefault();
            sendMessage();
        }
    }
    
    function formatMessageTime(timestamp: string) {
        const date = new Date(timestamp);
        const now = new Date();
        
        if (date.toDateString() === now.toDateString()) {
            return format(date, 'HH:mm', { locale: th });
        }
        
        return format(date, 'd MMM HH:mm', { locale: th });
    }
    
    function getMessageStatusIcon(status: string) {
        switch (status) {
            case 'SENDING':
                return '⏱️';
            case 'SENT':
                return '✓';
            case 'DELIVERED':
                return '✓✓';
            case 'READ':
                return '👁️';
            case 'FAILED':
                return '❌';
            default:
                return '';
        }
    }
    
    function groupMessagesByDate(messages: Message[]) {
        const groups: { date: string; messages: Message[] }[] = [];
        let currentDate = '';
        
        messages.forEach(message => {
            const messageDate = format(new Date(message.created_on), 'yyyy-MM-dd');
            
            if (messageDate !== currentDate) {
                currentDate = messageDate;
                groups.push({
                    date: messageDate,
                    messages: [message]
                });
            } else {
                groups[groups.length - 1].messages.push(message);
            }
        });
        
        return groups;
    }
    
    function formatDateHeader(dateStr: string) {
        const date = new Date(dateStr);
        const now = new Date();
        const yesterday = new Date(now);
        yesterday.setDate(yesterday.getDate() - 1);
        
        if (date.toDateString() === now.toDateString()) {
            return 'วันนี้';
        } else if (date.toDateString() === yesterday.toDateString()) {
            return 'เมื่อวาน';
        }
        
        return format(date, 'd MMMM yyyy', { locale: th });
    }
</script>

<div class="flex flex-col h-full bg-gray-50">
    <!-- Messages container -->
    <div
        bind:this={messageContainer}
        on:scroll={handleScroll}
        class="flex-1 overflow-y-auto p-4 space-y-4"
    >
        {#if channel?.loading && messages.length === 0}
            <div class="flex items-center justify-center h-full">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            </div>
        {:else if channel?.error}
            <div class="text-center text-red-600 p-4">
                {channel.error}
            </div>
        {:else}
            <!-- Load more indicator -->
            {#if isLoadingMore}
                <div class="text-center py-2">
                    <div class="inline-flex items-center gap-2 text-gray-500">
                        <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-500"></div>
                        <span>Loading more messages...</span>
                    </div>
                </div>
            {/if}
            
            <!-- Messages grouped by date -->
            {#each groupMessagesByDate(messages) as group}
                <!-- Date header -->
                <div class="flex items-center justify-center my-4">
                    <div class="bg-gray-200 text-gray-600 text-xs px-3 py-1 rounded-full">
                        {formatDateHeader(group.date)}
                    </div>
                </div>
                
                <!-- Messages in this date group -->
                {#each group.messages as message (message.id)}
                    <div class="flex {message.is_self ? 'justify-end' : 'justify-start'}">
                        <div class="max-w-xs lg:max-w-md">
                            <!-- Message bubble -->
                            <div
                                class="rounded-lg px-4 py-2 {message.is_self 
                                    ? 'bg-blue-500 text-white' 
                                    : 'bg-white text-gray-800 border border-gray-200'}"
                            >
                                <!-- User name for customer messages -->
                                {#if !message.is_self}
                                    <div class="text-xs {message.is_self ? 'text-blue-100' : 'text-gray-500'} mb-1">
                                        {message.user_name}
                                    </div>
                                {/if}
                                
                                <!-- Message content -->
                                {#if message.message_type === 'TEXT'}
                                    <p class="whitespace-pre-wrap break-words">{message.message}</p>
                                {:else if message.message_type === 'IMAGE'}
                                    {#if message.file_url}
                                        <img 
                                            src={message.file_url} 
                                            alt="Image" 
                                            class="rounded max-w-full cursor-pointer"
                                            on:click={() => window.open(message.file_url, '_blank')}
                                        />
                                    {:else}
                                        <p class="italic">[Image]</p>
                                    {/if}
                                {:else if message.message_type === 'FILE'}
                                    {#if message.file_url}
                                        <a 
                                            href={message.file_url} 
                                            target="_blank"
                                            class="flex items-center gap-2 underline"
                                        >
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                                      d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                            </svg>
                                            <span>{message.message}</span>
                                        </a>
                                    {:else}
                                        <p class="italic">{message.message}</p>
                                    {/if}
                                {/if}
                            </div>
                            
                            <!-- Message metadata -->
                            <div class="flex items-center gap-2 mt-1 text-xs text-gray-500">
                                <span>{formatMessageTime(message.created_on)}</span>
                                {#if message.is_self}
                                    <span>{getMessageStatusIcon(message.status)}</span>
                                {/if}
                            </div>
                        </div>
                    </div>
                {/each}
            {/each}
            
            <!-- Typing indicator -->
            {#if $platformChannelStore.typingIndicators.get(platformId)}
                <div class="flex justify-start">
                    <div class="bg-gray-200 rounded-lg px-4 py-2">
                        <div class="flex gap-1">
                            <span class="w-2 h-2 bg-gray-500 rounded-full animate-bounce" 
                                  style="animation-delay: 0ms"></span>
                            <span class="w-2 h-2 bg-gray-500 rounded-full animate-bounce" 
                                  style="animation-delay: 150ms"></span>
                            <span class="w-2 h-2 bg-gray-500 rounded-full animate-bounce" 
                                  style="animation-delay: 300ms"></span>
                        </div>
                    </div>
                </div>
            {/if}
        {/if}
    </div>
    
    <!-- Message input -->
    <div class="border-t border-gray-200 bg-white p-4">
        <form on:submit|preventDefault={sendMessage} class="flex gap-2">
            <textarea
                bind:value={messageInput}
                on:keypress={handleKeyPress}
                placeholder="พิมพ์ข้อความ..."
                rows="1"
                class="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none 
                       focus:ring-2 focus:ring-blue-500 resize-none"
                disabled={isSending}
            />
            
            <button
                type="submit"
                disabled={!messageInput.trim() || isSending}
                class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 
                       focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 
                       disabled:cursor-not-allowed transition-colors"
            >
                {#if isSending}
                    <svg class="animate-spin h-5 w-5" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" 
                                stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" 
                              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                {:else}
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                    </svg>
                {/if}
            </button>
        </form>
    </div>
</div>

<style>
    textarea {
        min-height: 40px;
        max-height: 120px;
    }
    
    @keyframes bounce {
        0%, 60%, 100% {
            transform: translateY(0);
        }
        30% {
            transform: translateY(-10px);
        }
    }
</style>