<script lang="ts">
    import { onMount } from 'svelte';
    import { customerStore } from '$lib/stores/customerStore';
    import type { Customer, CustomerPlatformIdentity } from '$lib/types/customer';
    import { format } from 'date-fns';
    import { th } from 'date-fns/locale';
    import PlatformLinkingModal from './PlatformLinkingModal.svelte';
    
    import { getBackendUrl } from '$src/lib/config';

    export let customerId: number;
    
    let customer: Customer | null = null;
    let isLoading = true;
    let isEditing = false;
    let showLinkingModal = false;
    let customerStats: any = null;
    let editForm = {
        name: '',
        email: '',
        phone: '',
        notes: ''
    };
    
    onMount(async () => {
        await loadCustomerDetails();
        // await loadCustomerStats();
    });
    
    async function loadCustomerDetails() {
        isLoading = true;
        try {
            const response = await fetch(`${getBackendUrl()}/customer/api/customers/${customerId}/`);
            if (response.ok) {
                customer = await response.json();
                // Update form with customer data
                editForm = {
                    name: customer.name || '',
                    email: customer.email || '',
                    phone: customer.phone || '',
                    notes: customer.notes || ''
                };
            }
        } catch (error) {
            console.error('Error loading customer details:', error);
        } finally {
            isLoading = false;
        }
    }
    
    // async function loadCustomerStats() {
    //     try {
    //         const response = await fetch(`${getBackendUrl()}/customer/api/customers/${customerId}/stats/`);
    //         if (response.ok) {
    //             customerStats = await response.json();
    //         }
    //     } catch (error) {
    //         console.error('Error loading customer stats:', error);
    //     }
    // }
    
    function handleEditCustomer() {
        isEditing = true;
    }
    
    async function saveCustomerDetails() {
        try {
            const response = await fetch(`${getBackendUrl()}/customer/api/customers/${customerId}/`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(editForm)
            });
            
            if (response.ok) {
                customer = await response.json();
                customerStore.updateCustomer(customer);
                isEditing = false;
            }
        } catch (error) {
            console.error('Error saving customer details:', error);
        }
    }
    
    function cancelEdit() {
        // Reset form to original values
        if (customer) {
            editForm = {
                name: customer.name || '',
                email: customer.email || '',
                phone: customer.phone || '',
                notes: customer.notes || ''
            };
        }
        isEditing = false;
    }
    
    function showLinkingCode() {
        showLinkingModal = true;
    }
    
    function handleLinkingComplete() {
        showLinkingModal = false;
        // Reload customer details to get updated platforms
        loadCustomerDetails();
    }
    
    function formatDate(dateString: string | null) {
        if (!dateString) return 'ไม่ระบุ';
        return format(new Date(dateString), 'd MMMM yyyy', { locale: th });
    }
    
    function getCustomerTypeLabel(type: string) {
        const types: Record<string, string> = {
            'PROSPECT': 'ผู้สนใจ',
            'NEW': 'ลูกค้าใหม่',
            'REGULAR': 'ลูกค้าประจำ',
            'VIP': 'ลูกค้า VIP',
            'INACTIVE': 'ไม่ใช้งาน'
        };
        return types[type] || type;
    }
    
    function getAccountStatusLabel(status: string) {
        const statuses: Record<string, string> = {
            'ACTIVE': 'ใช้งาน',
            'INACTIVE': 'ไม่ใช้งาน',
            'SUSPENDED': 'ระงับชั่วคราว',
            'BLACKLISTED': 'บัญชีดำ'
        };
        return statuses[status] || status;
    }
    
    function getStatusColor(status: string) {
        const colors: Record<string, string> = {
            'ACTIVE': 'text-green-600',
            'INACTIVE': 'text-gray-600',
            'SUSPENDED': 'text-yellow-600',
            'BLACKLISTED': 'text-red-600'
        };
        return colors[status] || 'text-gray-600';
    }
</script>

<div class="h-full flex flex-col bg-white">
    {#if isLoading}
        <div class="flex-1 flex items-center justify-center">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        </div>
    {:else if customer}
        <!-- Header -->
        <div class="p-4 border-b border-gray-200">
            <div class="flex items-start justify-between">
                <div>
                    <h2 class="text-xl font-semibold text-gray-900">
                        {customer.name || `Customer ${customer.customer_id}`}
                    </h2>
                    <div class="mt-1 flex items-center gap-3 text-sm">
                        <span class="text-gray-500">ID: {customer.customer_id}</span>
                        <span class={getStatusColor(customer.account_status)}>
                            ● {getAccountStatusLabel(customer.account_status)}
                        </span>
                    </div>
                </div>
                
                <button
                    on:click={handleEditCustomer}
                    class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded"
                    title="แก้ไขข้อมูล"
                >
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                </button>
            </div>
        </div>
        
        <!-- Content -->
        <div class="flex-1 overflow-y-auto">
            {#if isEditing}
                <!-- Edit Form -->
                <form on:submit|preventDefault={saveCustomerDetails} class="p-4 space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">
                            ชื่อ-นามสกุล
                        </label>
                        <input
                            type="text"
                            bind:value={editForm.name}
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg 
                                   focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">
                            อีเมล
                        </label>
                        <input
                            type="email"
                            bind:value={editForm.email}
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg 
                                   focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">
                            เบอร์โทร
                        </label>
                        <input
                            type="tel"
                            bind:value={editForm.phone}
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg 
                                   focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">
                            หมายเหตุ
                        </label>
                        <textarea
                            bind:value={editForm.notes}
                            rows="3"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg 
                                   focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                    </div>
                    
                    <div class="flex gap-2">
                        <button
                            type="submit"
                            class="px-4 py-2 bg-blue-500 text-white rounded-lg 
                                   hover:bg-blue-600 focus:outline-none focus:ring-2 
                                   focus:ring-blue-500"
                        >
                            บันทึก
                        </button>
                        <button
                            type="button"
                            on:click={cancelEdit}
                            class="px-4 py-2 border border-gray-300 text-gray-700 
                                   rounded-lg hover:bg-gray-50"
                        >
                            ยกเลิก
                        </button>
                    </div>
                </form>
            {:else}
                <!-- Customer Details -->
                <div class="p-4 space-y-6">
                    <!-- Basic Info -->
                    <section>
                        <h3 class="text-lg font-medium text-gray-900 mb-3">ข้อมูลทั่วไป</h3>
                        <dl class="space-y-2">
                            <div class="flex">
                                <dt class="w-1/3 text-sm text-gray-500">ประเภท:</dt>
                                <dd class="w-2/3 text-sm font-medium">
                                    {getCustomerTypeLabel(customer.customer_type)}
                                </dd>
                            </div>
                            <div class="flex">
                                <dt class="w-1/3 text-sm text-gray-500">อีเมล:</dt>
                                <dd class="w-2/3 text-sm">
                                    {customer.email || 'ไม่ระบุ'}
                                    {#if customer.email_verified}
                                        <span class="text-green-600 ml-1" title="ยืนยันแล้ว">✓</span>
                                    {/if}
                                </dd>
                            </div>
                            <div class="flex">
                                <dt class="w-1/3 text-sm text-gray-500">เบอร์โทร:</dt>
                                <dd class="w-2/3 text-sm">
                                    {customer.phone || 'ไม่ระบุ'}
                                    {#if customer.phone_verified}
                                        <span class="text-green-600 ml-1" title="ยืนยันแล้ว">✓</span>
                                    {/if}
                                </dd>
                            </div>
                            <div class="flex">
                                <dt class="w-1/3 text-sm text-gray-500">สมัครเมื่อ:</dt>
                                <dd class="w-2/3 text-sm">
                                    {formatDate(customer.created_on)}
                                </dd>
                            </div>
                        </dl>
                    </section>
                    
                    <!-- Platform Identities -->
                    <section>
                        <div class="flex items-center justify-between mb-3">
                            <h3 class="text-lg font-medium text-gray-900">
                                ช่องทางการติดต่อ
                            </h3>
                            <button
                                on:click={showLinkingCode}
                                class="text-sm text-blue-600 hover:text-blue-700"
                            >
                                + เชื่อมต่อช่องทางใหม่
                            </button>
                        </div>
                        
                        {#if customer.platform_identities && customer.platform_identities.length > 0}
                            <div class="space-y-2">
                                {#each customer.platform_identities as platform}
                                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                        <div class="flex items-center gap-3">
                                            <span class="text-2xl">
                                                {platform.platform === 'LINE' ? '💬' :
                                                 platform.platform === 'WHATSAPP' ? '📱' :
                                                 platform.platform === 'FACEBOOK' ? '👤' :
                                                 platform.platform === 'TELEGRAM' ? '✈️' :
                                                 platform.platform === 'INSTAGRAM' ? '📷' : '❓'}
                                            </span>
                                            <div>
                                                <div class="font-medium text-sm">
                                                    {platform.display_name || platform.platform}
                                                </div>
                                                <div class="text-xs text-gray-500">
                                                    {platform.platform_user_id}
                                                </div>
                                            </div>
                                        </div>
                                        {#if platform.is_verified}
                                            <span class="text-green-600" title="ยืนยันแล้ว">
                                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                                </svg>
                                            </span>
                                        {/if}
                                    </div>
                                {/each}
                            </div>
                        {:else}
                            <p class="text-sm text-gray-500">ยังไม่มีช่องทางที่เชื่อมต่อ</p>
                        {/if}
                    </section>
                    
                    <!-- Statistics -->
                    {#if customerStats}
                        <section>
                            <h3 class="text-lg font-medium text-gray-900 mb-3">สถิติ</h3>
                            <div class="grid grid-cols-2 gap-4">
                                <div class="bg-blue-50 p-3 rounded-lg">
                                    <div class="text-2xl font-bold text-blue-600">
                                        {customerStats.total_tickets || 0}
                                    </div>
                                    <div class="text-sm text-gray-600">ตั๋วทั้งหมด</div>
                                </div>
                                <div class="bg-green-50 p-3 rounded-lg">
                                    <div class="text-2xl font-bold text-green-600">
                                        {customerStats.open_tickets || 0}
                                    </div>
                                    <div class="text-sm text-gray-600">ตั๋วเปิด</div>
                                </div>
                                <div class="bg-purple-50 p-3 rounded-lg">
                                    <div class="text-2xl font-bold text-purple-600">
                                        {customerStats.total_messages || 0}
                                    </div>
                                    <div class="text-sm text-gray-600">ข้อความ</div>
                                </div>
                                <div class="bg-orange-50 p-3 rounded-lg">
                                    <div class="text-2xl font-bold text-orange-600">
                                        {customerStats.avg_response_time || 'N/A'}
                                    </div>
                                    <div class="text-sm text-gray-600">เวลาตอบกลับเฉลี่ย</div>
                                </div>
                            </div>
                        </section>
                    {/if}
                    
                    <!-- Tags -->
                    {#if customer.tags && customer.tags.length > 0}
                        <section>
                            <h3 class="text-lg font-medium text-gray-900 mb-3">แท็ก</h3>
                            <div class="flex flex-wrap gap-2">
                                {#each customer.tags as tag}
                                    <span class="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full">
                                        {tag.name}
                                    </span>
                                {/each}
                            </div>
                        </section>
                    {/if}
                    
                    <!-- Notes -->
                    {#if customer.notes}
                        <section>
                            <h3 class="text-lg font-medium text-gray-900 mb-3">หมายเหตุ</h3>
                            <p class="text-sm text-gray-700 whitespace-pre-wrap">
                                {customer.notes}
                            </p>
                        </section>
                    {/if}
                </div>
            {/if}
        </div>
    {/if}
    
    <!-- Linking Modal -->
    {#if showLinkingModal}
        <PlatformLinkingModal
            {customerId}
            on:close={() => showLinkingModal = false}
            on:complete={handleLinkingComplete}
        />
    {/if}
</div>

<style>
    /* Custom scrollbar */
    .overflow-y-auto {
        scrollbar-width: thin;
        scrollbar-color: #CBD5E0 #F7FAFC;
    }
    
    .overflow-y-auto::-webkit-scrollbar {
        width: 6px;
    }
    
    .overflow-y-auto::-webkit-scrollbar-track {
        background: #F7FAFC;
    }
    
    .overflow-y-auto::-webkit-scrollbar-thumb {
        background-color: #CBD5E0;
        border-radius: 3px;
    }
</style>