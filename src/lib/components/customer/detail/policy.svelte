<script lang="ts">
    import { t } from '$lib/stores/i18n';
    import { 
        TicketSolid, 
        ClipboardListSolid, 
        CalendarMonthSolid, 
        ClockSolid, 
        CloseCircleSolid,
    } from 'flowbite-svelte-icons';

    export let customer_policies: any;

    let showMore = false;
    // Display 3 policies initially, and toggle the rest
    $: visiblePolicies = showMore
        ? customer_policies.policies
        : customer_policies.policies.slice(0, 3);

    // Function to format date
    const displayDateDraft = (timestamp) => {
        const displayCreated = new Date(timestamp);
        
        // Format each part separately
        const day = displayCreated.getDate().toString().padStart(2, '0');
        const month = displayCreated.toLocaleString('en-US', { month: 'short' });
        const year = displayCreated.getFullYear();
        
        // Add hour and minute in 24-hour format
        const hour = displayCreated.getHours().toString().padStart(2, '0');
        const minute = displayCreated.getMinutes().toString().padStart(2, '0');
        
        // Combine in desired format
        return `${day} ${month} ${year} ${hour}:${minute}`;
    };
</script>

<!-- <p class="text-sm text-gray-500 dark:text-gray-400">
<b>Ticket:</b>
</p> -->
<!-- Policy Statistics-->
<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
    <!-- Total Policies -->
    <div class="bg-gray-100 rounded-lg p-4 flex items-center">
        <div class="p-2 rounded-full bg-gray-200 mr-3">
            <TicketSolid class="h-6 w-6 text-gray-700" />
        </div>
        <div>
            <p class="text-gray-500 text-sm">{t('total_policies')}</p>
            <p class="text-2xl font-bold">{customer_policies.statistics.total_policies}</p>
        </div>
    </div>

    <!-- Active Policies -->
    <div class="bg-green-100 rounded-lg p-4 flex items-center">
        <div class="p-2 rounded-full bg-green-200 mr-3">
            <ClipboardListSolid class="h-6 w-6 text-gray-700" />
        </div>
        <div>
            <p class="text-green-500 text-sm">{t('active')}</p>
            <p class="text-2xl font-bold">{customer_policies.statistics.active_policies}</p>
        </div>
    </div>

    <!-- Waiting Period Policies -->
    <div class="bg-blue-100 rounded-lg p-4 flex items-center">
        <div class="p-2 rounded-full bg-blue-200 mr-3">
            <CalendarMonthSolid class="h-6 w-6 text-blue-700" />
        </div>
        <div>
            <p class="text-blue-500 text-sm">{t('waiting_period')}</p>
            <p class="text-2xl font-bold">{customer_policies.statistics.waiting_period_policies}</p>
        </div>
    </div>

    <!-- Nearly Expired Policies -->
    <div class="bg-yellow-100 rounded-lg p-4 flex items-center">
        <div class="p-2 rounded-full bg-yellow-200 mr-3">
            <ClockSolid class="h-6 w-6 text-yellow-700" />
        </div>
        <div>
            <p class="text-yellow-500 text-sm">{t('nearly_expired')}</p>
            <p class="text-2xl font-bold">{customer_policies.statistics.nearly_expired_policies}</p>
        </div>
    </div>

    <!-- Expired Policies -->
    <div class="bg-red-100 rounded-lg p-4 flex items-center">
        <div class="p-2 rounded-full bg-red-200 mr-3">
            <CloseCircleSolid class="h-6 w-6 text-red-700" />
        </div>
        <div>
            <p class="text-red-500 text-sm">{t('expired')}</p>
            <p class="text-2xl font-bold">{customer_policies.statistics.expired_policies}</p>
        </div>
    </div>
</div>

<!-- Policy details -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 justify-start mb-4">
    {#each visiblePolicies as policy, index}
        <div class="w-full max-w-sm rounded-xl bg-white shadow-md p-6 text-left">
            <h2 class="text-xl font-bold text-gray-800 mb-4">
                {t('policy_details')} #{index + 1}
            </h2>

            <div class="space-y-4 text-sm">
                <!-- Product Name -->
                <div class="flex justify-between border-b pb-1">
                    <span class="text-gray-500">{t('product_name')}</span>
                    <span class="font-medium text-gray-800">{policy.product?.name ?? 'N/A'}</span>
                </div>

                <!-- Product Type -->
                <div class="flex justify-between border-b pb-1">
                    <span class="text-gray-500">{t('product_type')}</span>
                    <span class="font-medium text-gray-800">{policy.product?.product_type ?? 'N/A'}</span>
                </div>

                <!-- Status -->
                <div class="flex justify-between border-b pb-1">
                    <span class="text-gray-500">{t('status')}</span>
                    <span class={`font-semibold ${policy.policy_status === 'ACTIVE' ? 'text-green-600' : 'text-yellow-600'}`}>
                        {policy.policy_status}
                    </span>
                </div>

                <!-- Issue Date -->
                <div class="flex justify-between border-b pb-1">
                    <span class="text-gray-500">{t('issue_date')}</span>
                    <span class="font-medium text-gray-800">{displayDateDraft(policy.issue_date)}</span>
                </div>

                <!-- Start Date -->
                <div class="flex justify-between border-b pb-1">
                    <span class="text-gray-500">{t('start_date')}</span>
                    <span class="font-medium text-gray-800">{displayDateDraft(policy.start_date)}</span>
                </div>

                <!-- End Date -->
                <div class="flex justify-between">
                    <span class="text-gray-500">{t('end_date')}</span>
                    <span class="font-medium text-gray-800">{displayDateDraft(policy.end_date)}</span>
                </div>
            </div>
        </div>
    {/each}
</div>

<!-- See More / See Less button -->
{#if customer_policies.policies.length > 3}
    <div class="text-center">
        <button
            on:click={() => (showMore = !showMore)}
            class="px-4 py-2 rounded-lg text-sm font-semibold hover:bg-blue-700 transition"
        >
            {showMore ? t('see_less') : t('see_more')}
        </button>
    </div>
{/if}