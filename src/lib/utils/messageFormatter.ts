export function formatMessageTime(timestamp: string): string {
    const date = new Date(timestamp);
    const now = new Date();
    
    // If today, show time only
    if (isToday(date)) {
        return formatTime(date);
    }
    
    // If yesterday
    if (isYesterday(date)) {
        return `Yesterday ${formatTime(date)}`;
    }
    
    // If within this week
    if (isWithinWeek(date)) {
        return `${getDayName(date)} ${formatTime(date)}`;
    }
    
    // Otherwise show date and time
    return `${formatDate(date)} ${formatTime(date)}`;
}

export function formatRelativeTime(timestamp: string): string {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSec = Math.floor(diffMs / 1000);
    const diffMin = Math.floor(diffSec / 60);
    const diffHour = Math.floor(diffMin / 60);
    const diffDay = Math.floor(diffHour / 24);
    
    if (diffSec < 60) {
        return 'Just now';
    } else if (diffMin < 60) {
        return `${diffMin}m`;
    } else if (diffHour < 24) {
        return `${diffHour}h`;
    } else if (diffDay < 7) {
        return `${diffDay}d`;
    } else {
        return formatDate(date);
    }
}

export function truncateMessage(message: string, maxLength: number): string {
    if (!message) return '';
    if (message.length <= maxLength) return message;
    return message.substring(0, maxLength) + '...';
}

// Helper functions
function isToday(date: Date): boolean {
    const today = new Date();
    return date.getDate() === today.getDate() &&
           date.getMonth() === today.getMonth() &&
           date.getFullYear() === today.getFullYear();
}

function isYesterday(date: Date): boolean {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    return date.getDate() === yesterday.getDate() &&
           date.getMonth() === yesterday.getMonth() &&
           date.getFullYear() === yesterday.getFullYear();
}

function isWithinWeek(date: Date): boolean {
    const weekAgo = new Date();
    weekAgo.setDate(weekAgo.getDate() - 7);
    return date > weekAgo;
}

function formatTime(date: Date): string {
    return date.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
    });
}

function formatDate(date: Date): string {
    return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: date.getFullYear() !== new Date().getFullYear() ? 'numeric' : undefined
    });
}

function getDayName(date: Date): string {
    return date.toLocaleDateString('en-US', { weekday: 'long' });
}