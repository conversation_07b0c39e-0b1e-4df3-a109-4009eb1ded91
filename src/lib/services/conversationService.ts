import type { Message } from '$lib/types/customer';
import { getBackendUrl } from '$src/lib/config';

export class ConversationService {
    // private baseUrl = '/api/customers';
    private baseUrl = `${getBackendUrl}/customer/api/customers`;

    async getMessages(
        customerId: number,
        platformId: number,
        before?: number,
        limit: number = 50
    ): Promise<{
        messages: Message[];
        has_more: boolean;
    }> {
        let url = `${this.baseUrl}/${customerId}/platforms/${platformId}/messages/?limit=${limit}`;
        if (before) {
            url += `&before=${before}`;
        }

        const response = await fetch(url);
        if (!response.ok) {
            throw new Error('Failed to fetch messages');
        }
        return response.json();
    }

    async sendMessage(
        customerId: number,
        platformId: number,
        content: string,
        type: string = 'TEXT'
    ): Promise<Message> {
        const response = await fetch(
            `${this.baseUrl}/${customerId}/platforms/${platformId}/messages/`,
            {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    message: content,
                    message_type: type,
                }),
            }
        );

        if (!response.ok) {
            throw new Error('Failed to send message');
        }
        return response.json();
    }

    async markAsRead(
        customerId: number,
        platformId: number,
        messageIds: number[]
    ): Promise<void> {
        const response = await fetch(
            `${this.baseUrl}/${customerId}/platforms/${platformId}/read/`,
            {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    message_ids: messageIds,
                }),
            }
        );

        if (!response.ok) {
            throw new Error('Failed to mark messages as read');
        }
    }

    async uploadFile(
        customerId: number,
        platformId: number,
        file: File
    ): Promise<Message> {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('message_type', 'FILE');

        const response = await fetch(
            `${this.baseUrl}/${customerId}/platforms/${platformId}/messages/`,
            {
                method: 'POST',
                body: formData,
            }
        );

        if (!response.ok) {
            throw new Error('Failed to upload file');
        }
        return response.json();
    }
}

export const conversationService = new ConversationService();