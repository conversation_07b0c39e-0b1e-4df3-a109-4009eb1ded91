// import type { PageServerLoad } from './$types';
// import { error } from '@sveltejs/kit';
// import { getBackendUrl } from '$src/lib/config';

// export const load: PageServerLoad = async ({ params, fetch, cookies }) => {
//     const { customerId } = params;
    
//     try {
//         // Check for access token in cookies
//         let access_token = cookies.get('access_token');
//         let refresh_token = cookies.get('refresh_token');
//         if (!access_token) {
//             throw error(401, 'No access token available');
//         }

//         // Fetch customer details
//         // const customerResponse = await fetch(`/api/customers/${customerId}/`);
//         const customerResponse = await fetch(`${getBackendUrl()}/customer/api/customers/${customerId}/`,
//             {
//                 method: 'GET',
//                 headers: {
//                     'Authorization': `Bearer ${access_token}`,
//                     'Content-Type': 'application/json'
//                 }
//             }
//         );
        
//         if (!customerResponse.ok) {
//             if (customerResponse.status === 404) {
//                 throw error(404, 'Customer not found');
//             }
//             throw error(customerResponse.status, 'Failed to load customer');
//         }
        
//         const customer = await customerResponse.json();
        
//         // You could also pre-fetch platform identities here if needed
//         const platformsResponse = await fetch(`${getBackendUrl()}/customer/api/customers/${customerId}/platform-identities/`,
//             {
//                 method: 'GET',
//                 headers: {
//                     'Authorization': `Bearer ${access_token}`,
//                     'Content-Type': 'application/json'
//                 }
//             }
//         );
//         const platforms = await platformsResponse.json();
        
//         return {
//             customer,
//             platforms: platforms.results || []
//         };
//     } catch (err) {
//         console.error('Error loading customer:', err);
//         if (err instanceof Error && 'status' in err) {
//             throw err;
//         }
//         throw error(500, 'Failed to load customer details');
//     }
// };







































// // 2nd version - work but there is a problem of list of ONE option in the left-section
// import type { PageServerLoad } from './$types';
// import { error } from '@sveltejs/kit';
// import { getBackendUrl } from '$src/lib/config';

// export const load: PageServerLoad = async ({ params, fetch, cookies, url }) => {
//     const { customerId } = params;
//     const platformId = url.searchParams.get('platform');
    
//     try {
//         // Check for access token in cookies
//         let access_token = cookies.get('access_token');
//         let refresh_token = cookies.get('refresh_token');
//         if (!access_token) {
//             throw error(401, 'No access token available');
//         }

//         // Fetch customer details
//         const customerResponse = await fetch(`${getBackendUrl()}/customer/api/customers/${customerId}/`,
//             {
//                 method: 'GET',
//                 headers: {
//                     'Authorization': `Bearer ${access_token}`,
//                     'Content-Type': 'application/json'
//                 }
//             }
//         );
        
//         if (!customerResponse.ok) {
//             if (customerResponse.status === 404) {
//                 throw error(404, 'Customer not found');
//             }
//             throw error(customerResponse.status, 'Failed to load customer');
//         }
        
//         const customer = await customerResponse.json();
        
//         // Fetch all platform identities (not just for this customer)
//         const allPlatformsResponse = await fetch(`${getBackendUrl()}/customer/api/platform-identities/`,
//             {
//                 method: 'GET',
//                 headers: {
//                     'Authorization': `Bearer ${access_token}`,
//                     'Content-Type': 'application/json'
//                 }
//             }
//         );
//         const allPlatforms = await allPlatformsResponse.json();
        
//         return {
//             customer,
//             allPlatformIdentities: allPlatforms.results || [],
//             selectedPlatformId: platformId ? parseInt(platformId) : null
//         };
//     } catch (err) {
//         console.error('Error loading customer:', err);
//         if (err instanceof Error && 'status' in err) {
//             throw err;
//         }
//         throw error(500, 'Failed to load customer details');
//     }
// };














// // 3rd version - 1st-try
// import type { PageServerLoad } from './$types';
// import { error } from '@sveltejs/kit';
// import { getBackendUrl } from '$src/lib/config';

// export const load: PageServerLoad = async ({ params, fetch, cookies, url }) => {
//     const { customerId } = params;
//     const platformId = url.searchParams.get('platformId');
    
//     try {
//         // Check for access token in cookies
//         let access_token = cookies.get('access_token');
//         if (!access_token) {
//             throw error(401, 'No access token available');
//         }

//         // Fetch customer details
//         const customerResponse = await fetch(`${getBackendUrl()}/customer/api/customers/${customerId}/`,
//             {
//                 method: 'GET',
//                 headers: {
//                     'Authorization': `Bearer ${access_token}`,
//                     'Content-Type': 'application/json'
//                 }
//             }
//         );
        
//         if (!customerResponse.ok) {
//             if (customerResponse.status === 404) {
//                 throw error(404, 'Customer not found');
//             }
//             throw error(customerResponse.status, 'Failed to load customer');
//         }
        
//         const customer = await customerResponse.json();
        
//         // Fetch platform identities for this customer
//         const platformsResponse = await fetch(`${getBackendUrl()}/customer/api/customers/${customerId}/platform-identities/`,
//             {
//                 method: 'GET',
//                 headers: {
//                     'Authorization': `Bearer ${access_token}`,
//                     'Content-Type': 'application/json'
//                 }
//             }
//         );
//         const platforms = await platformsResponse.json();
        
//         return {
//             customer,
//             platforms: platforms.results || [],
//             selectedPlatformId: platformId ? parseInt(platformId) : null
//         };
//     } catch (err) {
//         console.error('Error loading customer:', err);
//         if (err instanceof Error && 'status' in err) {
//             throw err;
//         }
//         throw error(500, 'Failed to load customer details');
//     }
// };

// 4th version - 2nd-try
import type { PageServerLoad } from './$types';
import { error } from '@sveltejs/kit';
import { getBackendUrl } from '$src/lib/config';
import { services } from '$src/lib/api/features';

export const load: PageServerLoad = async ({ params, fetch, cookies, url }) => {
    const { customerId } = params;
    const platformId = url.searchParams.get('platformId');
    
    try {
        // Check for access token in cookies
        let access_token = cookies.get('access_token');
        if (!access_token) {
            throw error(401, 'No access token available');
        }

        // Fetch customer details
        const customerResponse = await fetch(`${getBackendUrl()}/customer/api/customers/${customerId}/`,
            {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${access_token}`,
                    'Content-Type': 'application/json'
                }
            }
        );
        
        if (!customerResponse.ok) {
            if (customerResponse.status === 404) {
                throw error(404, 'Customer not found');
            }
            throw error(customerResponse.status, 'Failed to load customer');
        }
        
        const customer = await customerResponse.json();
        
        // IMPORTANT: Fetch ALL platform identities, not just for this customer
        const allPlatformsResponse = await fetch(`${getBackendUrl()}/customer/api/platform-identities/`,
            {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${access_token}`,
                    'Content-Type': 'application/json'
                }
            }
        );
        const allPlatforms = await allPlatformsResponse.json();
        
        return {
            customer,
            allPlatformIdentities: allPlatforms.results || [],
            selectedPlatformId: platformId ? parseInt(platformId) : null,
        };
    } catch (err) {
        console.error('Error loading customer:', err);
        if (err instanceof Error && 'status' in err) {
            throw err;
        }
        throw error(500, 'Failed to load customer details');
    }
};