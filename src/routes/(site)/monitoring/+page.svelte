<script lang="ts">
    import { Breadcrumb, BreadcrumbItem } from 'flowbite-svelte';
    import { t } from '$src/lib/stores/i18n';
    import { enhance } from '$app/forms';
    import {
        Dropdown,
        DropdownItem,
        Checkbox,
        Spinner,
        Input,
        Toggle,
        Label,
        Button,
        Drawer,
        CloseButton
    } from 'flowbite-svelte';
    import {
        EditSolid,
        ChevronDownOutline,
        TicketSolid,
        PhoneSolid,
        AdjustmentsHorizontalSolid,
        SearchOutline,
        InfoCircleSolid,
        ArrowRightOutline
    } from 'flowbite-svelte-icons';

    import type { PageData } from './$types';
    import { sineIn } from 'svelte/easing';
    import { TicketService } from '$src/lib/api/features/ticket/tickets.service';
    import TicketTable from '$src/lib/components/tickets/TicketTable.svelte';
    import TicketStatistics from '$src/lib/components/tickets/TicketStatistics.svelte';

    import {formatTimestamp } from '$lib/utils';

    export let data: PageData;
    
    $: ({ tickets, users, statuses, error, priorities, ticket_topics, loginUser } = data);    
    
    // Sort state for tickets (set to 'None' initially)
    let sortOrder: 'asc' | 'desc' | 'None' = 'None'; // Default to 'None'

    // Function to toggle sort order (ascending / descending)
    function toggleSortOrder() {
        if (sortOrder === 'None') {
            sortOrder = 'asc'; // Change to 'asc' if it's 'None'
        } else if (sortOrder === 'asc') {
            sortOrder = 'desc'; // Change to 'desc' if it's 'asc'
        } else {
            sortOrder = 'None'; // Reset to 'None' if it's 'desc'
        }

        if (sortOrder !== 'None') {
            tickets = [...tickets].sort((a, b) => {
                const dateA = new Date(a.updated_on).getTime();
                const dateB = new Date(b.updated_on).getTime();
                return sortOrder === 'asc' ? dateA - dateB : dateB - dateA;
            });
        }
    }

    let pageTitle = 'Tickets';

    // TODO - Replace name to selectedStatuses
    let selectedStatus = new Set(['All']);
    let selectedMainInterface = 'All';
    let selectedPriority = new Set(['All']);
    let selectedSentiment = new Set(['All']);

    const statusesName = ['All', 'open', 'assigned', 'waiting', 'closed']; //["default", "open", "close", "waiting to be assigned"]
    // TODO - Check this with Backend
    // const mainInterfaceOptions = ["All", "None", "Calling", "Line", "FBMessenger"];
    const mainInterfaceOptions = ['All', 'Undefine', 'LINE'];
    const priorityOptions = ['All', 'Immediately', 'High', 'Medium', 'Low'];
    const sentimentOptions = ['All', 'Positive', 'Neutral', 'Negative'];

    // Declare searchQuery
    let searchQuery = '';
    let viewMyTasks : boolean =  false;

    function toggleViewMyTasks()  {
        viewMyTasks = !viewMyTasks;
        console.log(viewMyTasks);
    }

    // Reactive filter to return tickets based on selected Ticket model's features
    $: filteredTickets = tickets.filter((ticket) => {
        currentPage =  1;
        const isTicketOwner = ticket.owner.email === loginUser?.email;
        const statusMatch = selectedStatus.has('All') || selectedStatus.has(ticket.status);
        const mainInterfaceMatch =
            selectedMainInterface === 'All' ||
            (selectedMainInterface === 'None' && ticket.customer.main_interface === 'None') ||
            //   TODO - Check this with Backend
            (selectedMainInterface === 'LINE' && ticket.customer.main_interface === 'LINE');
        const SentimentMatch = selectedSentiment.has('All') || selectedSentiment.has(ticket.latest_analysis?.sentiment);
        const priortyMatch = selectedPriority.has('All') || selectedPriority.has(ticket.priority.name);
        // multiple fields based on searchQuery
        const searchMatch =
            String(ticket.id).toLowerCase().includes(searchQuery.toLowerCase()) || //ID
            ticket.status.toLowerCase().includes(searchQuery.toLowerCase()) || //Status
            ticket.priority.name.toLowerCase().includes(searchQuery.toLowerCase()) || //Priority
            ticket.latest_analysis?.sentiment.toLowerCase().includes(searchQuery.toLowerCase()) || //Sentiment
            ticket.customer.name?.toLowerCase().includes(searchQuery.toLowerCase()) || //Real Customer Name
            // ticket.customer.line_user.display_name.toLowerCase().includes(searchQuery.toLowerCase()) || //Line Customer Name
            ticket.owner.name.toLowerCase().includes(searchQuery.toLowerCase()) ; //Agent
        
        return statusMatch && mainInterfaceMatch && searchMatch && priortyMatch && SentimentMatch && (isTicketOwner || !viewMyTasks);
    });

    // Reactive declaration for sorting the filtered users based on sortColumn and sortDirection
    $: sortedTickets = [...filteredTickets].sort((a, b) => {
        // First sort by the selected column
        let primarySort = 0;
        
        switch (sortColumn) {
            case 'id':
                primarySort = sortDirection === 'desc' ? b.id - a.id : a.id - b.id;
                break;
            case 'status':
                const statusAIndex = statusesName.indexOf(a.status);
                const statusBIndex = statusesName.indexOf(b.status);
                primarySort = sortDirection === 'desc' ? - statusAIndex + statusBIndex : - statusBIndex + statusAIndex;
                break;
            case 'priority':
                const priorityAIndex = priorities.findIndex(p => p.name === a.priority.name);
                const priorityBIndex = priorities.findIndex(p => p.name === b.priority.name);
                primarySort = sortDirection === 'desc' ? priorityAIndex - priorityBIndex : priorityBIndex - priorityAIndex;
                break;
            case 'sentiment':
                const sentimentA = a.latest_analysis?.sentiment || '';
                const sentimentB = b.latest_analysis?.sentiment || '';
                primarySort = sortDirection === 'desc' ? sentimentB.localeCompare(sentimentA) : sentimentA.localeCompare(sentimentB);
                break;
            case 'customer':
                // Get customer name from either regular customer or LINE user
                const customerNameA = a.customer?.name || a.customer?.line_user?.display_name || '';
                const customerNameB = b.customer?.name || b.customer?.line_user?.display_name || '';
                primarySort = sortDirection === 'desc' ? customerNameB.localeCompare(customerNameA) : customerNameA.localeCompare(customerNameB);
                break;
            case 'agent':
                const agentA = a.owner?.name || '';
                const agentB = b.owner?.name || '';
                primarySort = sortDirection === 'desc' ? agentB.localeCompare(agentA) : agentA.localeCompare(agentB);
                break;
            case 'created_on':
                const createdOnA = new Date(a.created_on).getTime();
                const createdOnB = new Date(b.created_on).getTime();
                primarySort = sortDirection === 'desc' ? - createdOnB + createdOnA : - createdOnA + createdOnB;
                break;
            case 'updated_on':
                const updatedOnA = new Date(a.updated_on).getTime();
                const updatedOnB = new Date(b.updated_on).getTime();
                primarySort = sortDirection === 'desc' ? - updatedOnB + updatedOnA : - updatedOnA + updatedOnB;
                break;
        }

        // If primary sort criteria are equal, sort by updated_on as secondary criteria
        if (primarySort === 0 && sortColumn !== 'updated_on') {
            const updatedOnA = new Date(a.updated_on).getTime();
            const updatedOnB = new Date(b.updated_on).getTime();
            return updatedOnB - updatedOnA; // Always sort by updated_on in descending order as secondary criteria
        }

        return primarySort;
    });

    // Sort state for each column
    let sortColumn = 'status'; // Default to sorting by status first
    let sortDirection = 'asc'; // Default to descending order

    // Function to toggle sorting
    function sortBy(column: string, direction: string) {
        if (sortColumn === column) {
            // Toggle direction if already sorted by this column
            sortDirection = direction;
        } else {
            sortColumn = column;
            sortDirection = 'asc';
        }
        console.log(sortColumn, sortDirection);
    }

    function togglePriority(priority) {
        if (priority === 'All') {
            selectedPriority = new Set(['All']);
        } else {
            selectedPriority.delete('All');
            if (selectedPriority.has(priority)) {
                selectedPriority.delete(priority);
                if (selectedPriority.size === 0) {
                    selectedPriority.add('All');
                }
            } else {
                selectedPriority.add(priority);
            }
        }
        selectedPriority = new Set(selectedPriority); // Trigger reactivity
    }

    function toggleSentiment(sentiment) {
        if (sentiment === 'All') {
            selectedSentiment = new Set(['All']);
        } else {
            selectedSentiment.delete('All');
            if (selectedSentiment.has(sentiment)) {
                selectedSentiment.delete(sentiment);
                if (selectedSentiment.size === 0) {
                    selectedSentiment.add('All');
                }
            } else {
                selectedSentiment.add(sentiment);
            }
        }
        selectedSentiment = new Set(selectedSentiment); // Trigger reactivity
    }

    function toggleStatus(status) {
        if (status === 'All') {
            selectedStatus = new Set(['All']);
        } else {
            selectedStatus.delete('All');
            if (selectedStatus.has(status)) {
                selectedStatus.delete(status);
                if (selectedStatus.size === 0) {
                    selectedStatus.add('All');
                }
            } else {
                selectedStatus.add(status);
            }
        }
        selectedStatus = new Set(selectedStatus); // Trigger reactivity
    }

    function resetFilters() {
        selectedStatus = new Set(['All']);
        selectedMainInterface = 'All';
        selectedSentiment = new Set(['All']);
        selectedPriority = new Set(['All']);
        sortOrder = 'None'; // Reset sort order to 'None'
        tickets = [...(data?.tickets || [])]; // Reset tickets to the original data
        sortColumn = 'id';
        sortDirection = 'desc'; // Default to descending order
    }

    function capitalize(str: string): string {
        return str
            .split(' ')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ');
    }

    let hidden6 = true;
    let transitionParamsRight = {
        x: 320,
        duration: 200,
        easing: sineIn
    };

    // Add logic for date filtering and toggling between daily and total
    let dateFilter = 'total'; // Can be 'total' or 'daily'
    let selectedDate = new Date(); // Today's date for daily filter
    
    // Filter tickets by the selected date (for daily stats)
    function getDailyTickets() {
        return filteredTickets.filter(ticket => {
        const ticketDate = new Date(ticket.date);
        return ticketDate.toDateString() === selectedDate.toDateString();
        });
    }

    // Filter tickets based on status (for total stats)
    function getTicketCount(status) {
        let ticketsToConsider = dateFilter === 'total' ? filteredTickets : getDailyTickets();
        return ticketsToConsider.filter(ticket => ticket.status === status).length;
    }

    // Toggle between daily and total
    function toggleDateFilter() {
        dateFilter = dateFilter === 'total' ? 'daily' : 'total';
    }

    //////////////// Pagination Logic ////////////////
    // pagination state variables
    let currentPage = 1;
    let itemsPerPage = 10;

    $: totalPages = Math.ceil(Math.max((sortedTickets ?? []).length, 1) / itemsPerPage);
    $: paginatedDocuments = (sortedTickets ?? []).slice(0, itemsPerPage);

    function updatePagination() {
        const idx = (currentPage - 1) * itemsPerPage;
        paginatedDocuments = sortedTickets.slice(idx, Math.min(idx + itemsPerPage, sortedTickets.length));
    }
    
    function updateCurrentPage(newCurrentPage: number) {
        currentPage = newCurrentPage;
        updatePagination();
    }

</script>

<svelte:head>
    <title>{pageTitle}</title>
</svelte:head>

<div class="flex h-screen bg-white">
    <div class="flex-1 overflow-y-auto bg-white p-8">
        <Breadcrumb aria-label="Default breadcrumb example" class="mb-3">
            <BreadcrumbItem href="/" home>
            <span class="text-gray-400">{t('home')}</span>
            </BreadcrumbItem>
            <BreadcrumbItem>
            <span class="text-gray-700">{t('tickets')}</span>
            </BreadcrumbItem>
        </Breadcrumb>

        <div class="mb-6">
            <div class="inline-flex flex-row gap-10 items-center">
                <span>
                    <h2 class="text-2xl font-bold">{t('tickets')}</h2>
                    <p class="text-gray-600">{t('tickets_description')}</p>
                </span>
                <Button
                    color={viewMyTasks ? 'dark' : 'none'}
                    class={`${
                        viewMyTasks ? '' : 'hover:bg-gray-100 border'
                    }`}
                    on:click={toggleViewMyTasks}
                >
                    {t('view_my_tickets')}
                </Button>
            </div>
        </div>
                
        
        <!-- Ticket Statistics Cards -->
        <TicketStatistics tickets={sortedTickets} />
        
        <!-- Filters and Search Bar - Improved Layout -->
        <div class="flex flex-col lg:flex-row items-start lg:items-center lg:justify-between mb-6 gap-4">
            <!-- Left side - Filter Buttons -->
            <div class="flex flex-wrap gap-3">
                <!-- Status Filter -->
                <div>
                    <Button 
                        color={!selectedStatus.has('All') ? 'dark' : 'none'}
                        class={`${!selectedStatus.has('All') ? '' : 'hover:bg-gray-100 border'}`}
                    >
                        <AdjustmentsHorizontalSolid class="w-4 h-4" />
                        <span>{t('status')}</span>
                        <ChevronDownOutline class="w-3 h-3" />
                    </Button>
                    <Dropdown class="w-44 p-2 shadow-lg">
                        {#each statusesName as statusName}
                            <div class="p-2 hover:bg-gray-100 rounded">
                                <Checkbox
                                    checked={selectedStatus.has(statusName)}
                                    on:change={() => toggleStatus(statusName)}
                                    class="flex items-center"
                                >
                                    <span class="ml-2 text-sm">
                                        {capitalize(statusName).replace('_', ' ')}
                                    </span>
                                </Checkbox>
                            </div>
                        {/each}
                    </Dropdown>
                </div>

                <!-- Priority Filter -->
                <div>
                    <Button
                        color={!selectedPriority.has('All') ? 'dark' : 'none'}
                        class={`${!selectedPriority.has('All') ? '' : 'hover:bg-gray-100 border'}`} 
                    >
                        <AdjustmentsHorizontalSolid class="w-4 h-4" />
                        <span>{t('priority')}</span>
                        <ChevronDownOutline class="w-3 h-3" />
                    </Button>
                    <Dropdown class="w-44 p-2 shadow-lg">
                        {#each priorityOptions as priorityName}
                            <div class="p-2 hover:bg-gray-100 rounded">
                                <Checkbox 
                                    checked={selectedPriority.has(priorityName)} 
                                    on:change={() => togglePriority(priorityName)}
                                    class="flex items-center"
                                >
                                    <span class="ml-2 text-sm">{priorityName.replace("_", " ")}</span>
                                </Checkbox>
                            </div>
                        {/each}
                    </Dropdown>
                </div>

                <!-- Sentiment Filter -->
                <div>
                    <Button
                        color={!selectedSentiment.has('All') ? 'dark' : 'none'}
                        class={`${!selectedSentiment.has('All') ? '' : 'hover:bg-gray-100 border'}`}  
                    >
                        <AdjustmentsHorizontalSolid class="w-4 h-4" />
                        <span>{t('sentiment')}</span>
                        <ChevronDownOutline class="w-3 h-3" />
                    </Button>
                    <Dropdown class="w-44 p-2 shadow-lg">
                        {#each sentimentOptions as sentimentName}
                            <div class="p-2 hover:bg-gray-100 rounded">
                                <Checkbox
                                    checked={selectedSentiment.has(sentimentName)}
                                    on:change={() => toggleSentiment(sentimentName)}
                                    class="flex items-center"
                                >
                                    <span class="ml-2 text-sm">{sentimentName.replace('_', ' ')}</span>
                                </Checkbox>
                            </div>
                        {/each}
                    </Dropdown>
                </div>

                <!-- Reset Filter -->
                <Button 
                    color="none" 
                    on:click={resetFilters}
                    class="hover:bg-gray-100 border"
                >
                    {t('reset_filters')}
                </Button>
            </div>

            <!-- Right side - Search Bar -->
            <div class="relative w-full lg:w-1/3">
                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                    <SearchOutline class="w-5 h-5 text-gray-500" />
                </div>
                <Input
                    id="searchBar"
                    type="text"
                    placeholder={t('search_placeholder')}
                    bind:value={searchQuery}
                    class={`bg-white block w-full pl-10 py-2.5 border rounded-lg
                        focus:outline-none focus:ring-2 focus:ring-blue-700 focus:border-blue-700
                        ${searchQuery ? 'border-blue-500 ring-2 ring-blue-500' : 'border-gray-300'}`}
                />
            </div>
        </div>
        
        <!-- Ticket Table Component -->
        <TicketTable 
            {tickets}
            {users}
            {statuses}
            {priorities}
            {ticket_topics}
            {loginUser}
            {currentPage}
            {itemsPerPage}
            {totalPages}
            {paginatedDocuments}
            {updateCurrentPage}
            {sortColumn}
            {sortDirection}
            {sortBy}
        />
    </div>
</div>