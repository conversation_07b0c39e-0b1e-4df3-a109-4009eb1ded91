<script lang="ts">
    import { onMount } from 'svelte';
    import { fetchDashboard, type DashboardData } from '$lib/api/features/dashboard/dashboard';
    import { Breadcrumb, BreadcrumbItem } from 'flowbite-svelte';
    
    let pageTitle = "Dashboard";
    let currentDashboard: DashboardData | null = null;
    let error: string = '';
    let activeId = 1;
    let isLoading = false;
    let isDropdownOpen = false;
    let iframeStyle = "position: absolute; top: -80px; left: 0;";
    let windowWidth: number;
    
    const dashboardOptions = [
        { id: 1, name: 'Customer Sentiment' },
        { id: 2, name: 'Unresolved Tickets Dashboard' },
        { id: 3, name: 'Human Agent Performance Dashboard (All)' },
        { id: 4, name: 'Human Agent Performance Dashboard (Single)' },
        { id: 5, name: 'Agent Online/Offline Statistics' },
        { id: 6, name: 'Customer Topic Breakdown' },
        { id: 7, name: 'Usefulness of AI' },
        { id: 8, name: 'Usefulness of AI 2' },
    ];

    async function loadDashboard(id: number) {
        isLoading = true;
        error = '';
        
        try {
            currentDashboard = await fetchDashboard(id);
            // console.log('Loaded dashboard:', currentDashboard);
        } catch (e) {
            error = `Failed to load dashboard ${id}`;
            // console.error(`Error loading dashboard ${id}:`, e);
            currentDashboard = null;
        } finally {
            isLoading = false;
        }
    }

    async function switchDashboard(id: number) {
        if (id === activeId) return;
        activeId = id;
        isDropdownOpen = false;
        await loadDashboard(activeId);
    }

    onMount(async () => {
        await loadDashboard(activeId);
    });

    function handleClickOutside(event: MouseEvent) {
        const target = event.target as HTMLElement;
        if (!target.closest('.dropdown')) {
            isDropdownOpen = false;
        }
    }

    $: {
        if (windowWidth >= 1200) {
            iframeStyle = "position: absolute; top: -80px; left: -300px; width: calc(100% + 300px);";
        } else {
            iframeStyle = "position: absolute; top: -80px; left: 0; width: 100%;";
        }
    }
</script>

<svelte:window 
    on:click={handleClickOutside}
    bind:innerWidth={windowWidth}
/>

<svelte:head>
    <title>{pageTitle}</title>
</svelte:head>

<!-- <div class="min-h-screen bg-gray-50">
    <div class="mx-auto px-4 sm:px-6 lg:px-8 pt-4"> -->
<div class="flex h-screen">
	<div class="w-full h-full bg-white px-8 pt-8">
        <Breadcrumb aria-label="Default breadcrumb example" class="mb-3">
            <BreadcrumbItem href="/" home>
                <span class="text-gray-400">Home</span>
            </BreadcrumbItem>
            <BreadcrumbItem>
                <span class="text-gray-700">Dashboard</span>
            </BreadcrumbItem>
        </Breadcrumb>
        <div class="flex items-center space-x-2">
            <h2 class="text-2xl font-bold">Dashboard</h2>
            {#if currentDashboard}
                <div class="flex items-center">
                    <span class="text-gray-500 mx-2">/</span>
                    <div class="relative dropdown">
                        <button 
                            class="flex items-center space-x-2 px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                            on:click={() => isDropdownOpen = !isDropdownOpen}
                        >
                            <span class="font-medium">{currentDashboard.title}</span>
                            <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>
                        
                        {#if isDropdownOpen}
                            <div class="absolute z-10 mt-2 w-56 rounded-lg shadow-lg bg-white ring-1 ring-black ring-opacity-5 border border-gray-200 transform transition-all duration-200 ease-out">
                                <div class="py-1" role="menu">
                                    {#each dashboardOptions as option}
                                        <button
                                            class="block w-full text-left px-4 py-3 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 transition-colors duration-150 {activeId === option.id ? 'bg-blue-50 text-blue-700 font-medium' : ''}"
                                            role="menuitem"
                                            on:click={() => switchDashboard(option.id)}
                                        >
                                            {option.name}
                                        </button>
                                    {/each}
                                </div>
                            </div>
                        {/if}
                    </div>
                </div>
            {/if}
        </div>

        <!-- Dashboard Content -->
        <div class="flex-1 overflow-hidden">
            {#if error}
                <div class="mt-6 bg-red-50 border border-red-200 rounded-lg p-4">
                    <p class="text-red-700">{error}</p>
                </div>
            {:else if currentDashboard?.dashboard_url}
                <div class="mt-6">
                    <div class="bg-white rounded-lg shadow">
                        <div>
                            <div style="height: 770px; overflow: hidden; position: relative;">
                                <iframe
                                    title="Unresolved Tickets"
                                    src={currentDashboard?.dashboard_url}
                                    width="calc(100% + 300px)"
                                    height="1000"
                                    style={iframeStyle}
                                    frameborder="0"
                                    scrolling="no"
                                ></iframe>
                            </div>
                        </div>
                    </div>
                </div>
            {/if}
        </div>
    </div>
</div>