{"name": "salmate-frontend", "version": "0.0.1", "private": true, "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "serve": "node build", "test": "npm run test:integration && npm run test:unit", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "lint": "prettier --check . && eslint .", "format": "prettier --write .", "test:integration": "playwright test", "test:unit": "vitest"}, "devDependencies": {"@playwright/test": "^1.28.1", "@sveltejs/adapter-auto": "^3.0.0", "@sveltejs/adapter-node": "^5.2.12", "@sveltejs/kit": "^2.0.0", "@sveltejs/vite-plugin-svelte": "^3.0.0", "@tailwindcss/typography": "^0.5.14", "@types/eslint": "^9.6.0", "autoprefixer": "^10.4.20", "eslint": "^9.0.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-svelte": "^2.36.0", "flowbite-svelte-icons": "^1.6.1", "globals": "^15.0.0", "prettier": "^3.1.1", "prettier-plugin-svelte": "^3.1.2", "prettier-plugin-tailwindcss": "^0.6.5", "svelte": "^4.2.7", "svelte-check": "^3.6.0", "tailwindcss": "^3.3.2", "typescript": "^5.0.0", "typescript-eslint": "^8.0.0", "vite": "^5.0.3", "vitest": "^2.0.0"}, "type": "module", "dependencies": {"@fortawesome/free-brands-svg-icons": "^6.6.0", "@fortawesome/free-solid-svg-icons": "^6.6.0", "bits-ui": "^0.21.13", "chart.js": "^4.4.4", "clsx": "^2.1.1", "date-fns": "^4.1.0", "date-picker-svelte": "^2.15.1", "flowbite": "^2.5.1", "flowbite-svelte": "^0.48.6", "formsnap": "^1.0.1", "svelte-fa": "^4.0.2", "svelte-i18n": "^4.0.1", "sveltekit-superforms": "^2.17.0", "svelty-picker": "^5.2.12", "tailwind-merge": "^2.5.2", "tailwind-variants": "^0.2.1", "timeago.js": "^4.0.2", "zod": "^3.23.8"}}